# API Report Documentation - iRestora PLUS

## Overview
API ini dibuat khusus untuk mengambil laporan harian dan bulanan dari sistem iRestora PLUS untuk integrasi dengan sistem eksternal. API ini terpisah dari controller dan model yang sudah ada sehingga tidak mengganggu fungsionalitas sistem yang berjalan.

## Base URL
```
http://your-domain.com/index.php/Api_Report/
```

## Authentication
Saat ini API menggunakan validasi outlet_id dan company_id. Untuk keamanan tambahan, bisa ditambahkan API key di masa depan.

---

## 1. Test Connection

### Endpoint
```
GET /test_connection
```

### Description
Test koneksi API untuk memastikan API berjalan dengan baik.

### Response
```json
{
    "status": true,
    "message": "API Report connection successful",
    "timestamp": "2024-12-25 10:30:00",
    "version": "1.0.0"
}
```

---

## 2. Daily Summary Report

### Endpoint
```
POST /daily_summary
```

### Description
Mengambil laporan ringkasan harian berdasarkan tanggal tertentu atau range tanggal.

### Parameters

#### Mode Single Date
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| date | string | No | Format: Y-m-d (contoh: 2024-12-25). Jika kosong, akan menggunakan hari ini |
| outlet_id | integer | Yes | ID outlet |
| company_id | integer | Yes | ID company |

#### Mode Range Date
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| start_date | string | Yes | Format: Y-m-d - Tanggal mulai |
| end_date | string | Yes | Format: Y-m-d - Tanggal akhir |
| outlet_id | integer | Yes | ID outlet |
| company_id | integer | Yes | ID company |

### Request Examples

#### Single Date
```bash
curl -X POST http://your-domain.com/index.php/Api_Report/daily_summary \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "date=2024-12-25&outlet_id=1&company_id=1"
```

#### Range Date
```bash
curl -X POST http://your-domain.com/index.php/Api_Report/daily_summary \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "start_date=2024-12-20&end_date=2024-12-25&outlet_id=1&company_id=1"
```

### Response Examples

#### Single Date Response
```json
{
    "status": true,
    "message": "Laporan harian berhasil diambil",
    "data": {
        "report_type": "single",
        "report_period": "2024-12-25",
        "outlet_id": "1",
        "company_id": "1",
        "outlet_name": "Kolam Renang Resto Lembah Cinta",
        "summary": {
            "purchases": {
                "total_amount": 500000,
                "total_paid": 300000,
                "total_due": 200000,
                "count": 5
            },
            "sales": {
                "total_amount": 1200000,
                "total_paid": 1100000,
                "total_due": 100000,
                "total_discount": 50000,
                "count": 25
            },
            "expenses": {
                "total_amount": 150000,
                "count": 3
            },
            "wastes": {
                "total_amount": 25000,
                "count": 2
            },
            "supplier_payments": {
                "total_amount": 100000,
                "count": 1
            },
            "customer_receives": {
                "total_amount": 50000,
                "count": 1
            },
            "net_cash_flow": 900000
        },
        "details": {
            "purchases": [...],
            "sales": [...],
            "expenses": [...],
            "wastes": [...],
            "supplier_payments": [...],
            "customer_receives": [...]
        }
    }
}
```

#### Range Date Response
```json
{
    "status": true,
    "message": "Laporan range tanggal berhasil diambil",
    "data": {
        "report_type": "range",
        "report_period": "2024-12-20 to 2024-12-25",
        "outlet_id": "1",
        "company_id": "1",
        "outlet_name": "Kolam Renang Resto Lembah Cinta",
        "summary": {
            "date_range": {
                "start_date": "2024-12-20",
                "end_date": "2024-12-25",
                "total_days": 6
            },
            "purchases": {
                "total_amount": 3000000,
                "total_paid": 2500000,
                "total_due": 500000,
                "count": 30
            },
            "sales": {
                "total_amount": 7200000,
                "total_paid": 7000000,
                "total_due": 200000,
                "total_discount": 300000,
                "count": 150
            },
            "net_cash_flow": 4500000
        },
        "daily_breakdown": [
            {
                "date": "2024-12-20",
                "day_name": "Friday",
                "sales": {
                    "count": 25,
                    "total_amount": 1200000,
                    "total_paid": 1100000
                },
                "purchases": {
                    "count": 5,
                    "total_amount": 500000,
                    "total_paid": 400000
                },
                "expenses": {
                    "count": 3,
                    "total_amount": 150000
                },
                "net_cash_flow": 550000
            }
            // ... data untuk tanggal lainnya
        ],
        "details": {
            "purchases": [...],
            "sales": [...],
            "expenses": [...],
            "note": "Detail transaksi dibatasi 100 record per kategori untuk performance"
        }
    }
}
```

---

## 3. Monthly Summary Report

### Endpoint
```
POST /monthly_summary
```

### Description
Mengambil laporan ringkasan bulanan berdasarkan bulan dan tahun tertentu.

### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| month | integer | No | Bulan (1-12). Jika kosong, akan menggunakan bulan ini |
| year | integer | No | Tahun (YYYY). Jika kosong, akan menggunakan tahun ini |
| outlet_id | integer | Yes | ID outlet |
| company_id | integer | Yes | ID company |

### Request Example
```bash
curl -X POST http://your-domain.com/index.php/Api_Report/monthly_summary \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "month=12&year=2024&outlet_id=1&company_id=1"
```

### Response Example
```json
{
    "status": true,
    "message": "Laporan bulanan berhasil diambil",
    "data": {
        "report_month": 12,
        "report_year": 2024,
        "month_name": "Desember",
        "outlet_id": "1",
        "company_id": "1",
        "outlet_name": "Kolam Renang Resto Lembah Cinta",
        "summary": {
            "purchases": {
                "total_amount": 15000000,
                "total_paid": 12000000,
                "total_due": 3000000,
                "count": 150
            },
            "sales": {
                "total_amount": 35000000,
                "total_paid": 33000000,
                "total_due": 2000000,
                "total_discount": 1500000,
                "count": 750
            },
            "expenses": {
                "total_amount": 4500000,
                "count": 90
            },
            "wastes": {
                "total_amount": 750000,
                "count": 60
            },
            "net_cash_flow": 16500000
        },
        "daily_breakdown": [
            {
                "date": "2024-12-01",
                "day_name": "Sunday",
                "sales": {
                    "count": 25,
                    "total_amount": 1200000,
                    "total_paid": 1100000
                },
                "purchases": {
                    "count": 5,
                    "total_amount": 500000,
                    "total_paid": 400000
                },
                "expenses": {
                    "count": 3,
                    "total_amount": 150000
                },
                "net_cash_flow": 550000
            }
            // ... data untuk tanggal lainnya
        ]
    }
}
```

---

## Error Responses

### 400 Bad Request
```json
{
    "status": false,
    "message": "outlet_id dan company_id wajib diisi",
    "data": null
}
```

### 401 Unauthorized
```json
{
    "status": false,
    "message": "outlet_id atau company_id tidak valid",
    "data": null
}
```

### 500 Internal Server Error
```json
{
    "status": false,
    "message": "Terjadi kesalahan: Database connection failed",
    "data": null
}
```

---

## Implementation Notes

1. **File Locations:**
   - Controller: `application/controllers/Api_Report.php`
   - Model: `application/models/Api_Report_model.php`

2. **Dependencies:**
   - Menggunakan REST_Controller library yang sudah ada
   - Menggunakan Common_model untuk timezone setting

3. **Security:**
   - Validasi outlet_id dan company_id
   - Input sanitization
   - Error handling yang proper

4. **Performance:**
   - Query yang dioptimasi
   - Menggunakan JOIN untuk mengurangi query
   - Caching bisa ditambahkan di masa depan

5. **Extensibility:**
   - Mudah ditambahkan endpoint baru
   - Structure yang konsisten
   - Dokumentasi yang lengkap
