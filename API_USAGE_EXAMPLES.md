# API Usage Examples - iRestora PLUS Report API

## PHP Examples

### 1. Daily Report - PHP with cURL
```php
<?php
function getDailyReport($date, $outlet_id, $company_id) {
    $url = 'http://your-domain.com/index.php/Api_Report/daily_summary';
    
    $data = array(
        'date' => $date,
        'outlet_id' => $outlet_id,
        'company_id' => $company_id
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        return json_decode($response, true);
    } else {
        return false;
    }
}

// Usage
$result = getDailyReport('2024-12-25', 1, 1);
if ($result && $result['status']) {
    echo "Total Sales: " . $result['data']['summary']['sales']['total_amount'];
    echo "Net Cash Flow: " . $result['data']['summary']['net_cash_flow'];
} else {
    echo "Error getting report";
}
?>
```

### 2. Monthly Report - PHP with cURL
```php
<?php
function getMonthlyReport($month, $year, $outlet_id, $company_id) {
    $url = 'http://your-domain.com/index.php/Api_Report/monthly_summary';
    
    $data = array(
        'month' => $month,
        'year' => $year,
        'outlet_id' => $outlet_id,
        'company_id' => $company_id
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Usage
$result = getMonthlyReport(12, 2024, 1, 1);
if ($result && $result['status']) {
    echo "Monthly Sales: " . $result['data']['summary']['sales']['total_amount'];
    
    // Loop through daily breakdown
    foreach ($result['data']['daily_breakdown'] as $day) {
        echo $day['date'] . ": " . $day['sales']['total_amount'] . "\n";
    }
}
?>
```

---

## JavaScript/Node.js Examples

### 1. Daily Report - JavaScript with Fetch
```javascript
async function getDailyReport(date, outletId, companyId) {
    const url = 'http://your-domain.com/index.php/Api_Report/daily_summary';
    
    const formData = new FormData();
    formData.append('date', date);
    formData.append('outlet_id', outletId);
    formData.append('company_id', companyId);
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.status) {
            console.log('Daily Report:', data.data);
            return data.data;
        } else {
            console.error('Error:', data.message);
            return null;
        }
    } catch (error) {
        console.error('Network error:', error);
        return null;
    }
}

// Usage
getDailyReport('2024-12-25', 1, 1).then(report => {
    if (report) {
        console.log('Total Sales:', report.summary.sales.total_amount);
        console.log('Net Cash Flow:', report.summary.net_cash_flow);
    }
});
```

### 2. Monthly Report - Node.js with Axios
```javascript
const axios = require('axios');

async function getMonthlyReport(month, year, outletId, companyId) {
    const url = 'http://your-domain.com/index.php/Api_Report/monthly_summary';
    
    const data = {
        month: month,
        year: year,
        outlet_id: outletId,
        company_id: companyId
    };
    
    try {
        const response = await axios.post(url, data, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        
        if (response.data.status) {
            return response.data.data;
        } else {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('Error getting monthly report:', error.message);
        return null;
    }
}

// Usage
getMonthlyReport(12, 2024, 1, 1).then(report => {
    if (report) {
        console.log('Monthly Sales:', report.summary.sales.total_amount);
        
        // Show daily breakdown
        report.daily_breakdown.forEach(day => {
            console.log(`${day.date}: ${day.sales.total_amount}`);
        });
    }
});
```

---

## Python Examples

### 1. Daily Report - Python with Requests
```python
import requests
import json
from datetime import datetime

def get_daily_report(date, outlet_id, company_id):
    url = 'http://your-domain.com/index.php/Api_Report/daily_summary'
    
    data = {
        'date': date,
        'outlet_id': outlet_id,
        'company_id': company_id
    }
    
    try:
        response = requests.post(url, data=data)
        response.raise_for_status()
        
        result = response.json()
        
        if result['status']:
            return result['data']
        else:
            print(f"Error: {result['message']}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Network error: {e}")
        return None

# Usage
report = get_daily_report('2024-12-25', 1, 1)
if report:
    print(f"Total Sales: {report['summary']['sales']['total_amount']}")
    print(f"Net Cash Flow: {report['summary']['net_cash_flow']}")
```

### 2. Monthly Report - Python Class
```python
import requests
import json
from datetime import datetime

class IRestoraAPI:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
    
    def get_monthly_report(self, month, year, outlet_id, company_id):
        url = f"{self.base_url}/index.php/Api_Report/monthly_summary"
        
        data = {
            'month': month,
            'year': year,
            'outlet_id': outlet_id,
            'company_id': company_id
        }
        
        try:
            response = requests.post(url, data=data)
            response.raise_for_status()
            
            result = response.json()
            
            if result['status']:
                return result['data']
            else:
                raise Exception(result['message'])
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Network error: {e}")
    
    def test_connection(self):
        url = f"{self.base_url}/index.php/Api_Report/test_connection"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            
            result = response.json()
            return result['status']
            
        except:
            return False

# Usage
api = IRestoraAPI('http://your-domain.com')

# Test connection first
if api.test_connection():
    print("API connection successful")
    
    # Get monthly report
    report = api.get_monthly_report(12, 2024, 1, 1)
    print(f"Monthly Sales: {report['summary']['sales']['total_amount']}")
    
    # Show top 5 days by sales
    daily_data = sorted(report['daily_breakdown'], 
                       key=lambda x: x['sales']['total_amount'], 
                       reverse=True)[:5]
    
    print("\nTop 5 days by sales:")
    for day in daily_data:
        print(f"{day['date']} ({day['day_name']}): {day['sales']['total_amount']}")
else:
    print("API connection failed")
```

---

## Integration Tips

### 1. Error Handling
```php
function handleApiResponse($response) {
    $data = json_decode($response, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON response');
    }
    
    if (!$data['status']) {
        throw new Exception('API Error: ' . $data['message']);
    }
    
    return $data['data'];
}
```

### 2. Caching Results
```php
function getCachedDailyReport($date, $outlet_id, $company_id) {
    $cache_key = "daily_report_{$date}_{$outlet_id}_{$company_id}";
    
    // Check cache first
    $cached = getFromCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    // Get from API
    $report = getDailyReport($date, $outlet_id, $company_id);
    
    // Cache for 1 hour
    if ($report) {
        setCache($cache_key, $report, 3600);
    }
    
    return $report;
}
```

### 3. Batch Processing
```python
def get_multiple_daily_reports(dates, outlet_id, company_id):
    reports = {}
    
    for date in dates:
        try:
            report = get_daily_report(date, outlet_id, company_id)
            if report:
                reports[date] = report
        except Exception as e:
            print(f"Error getting report for {date}: {e}")
    
    return reports

# Usage
dates = ['2024-12-01', '2024-12-02', '2024-12-03']
reports = get_multiple_daily_reports(dates, 1, 1)
```
