# API Report Module - iRestora PLUS

## Overview
Modul API Report ini dibuat khusus untuk mengintegrasikan sistem iRestora PLUS dengan sistem eksternal. API ini menyediakan endpoint untuk mengambil laporan harian dan bulanan tanpa mengganggu fungsionalitas sistem yang sudah ada.

## Features
- ✅ Laporan harian lengkap (penjualan, pembelian, pengeluaran, waste, dll)
- ✅ Laporan bulanan dengan breakdown harian
- ✅ Validasi outlet dan company
- ✅ Error handling yang proper
- ✅ Format response JSON yang konsisten
- ✅ Dokumentasi lengkap dan contoh penggunaan
- ✅ Test file untuk validasi

## File Structure
```
application/
├── controllers/
│   └── Api_Report.php          # Controller API utama
├── models/
│   └── Api_Report_model.php    # Model untuk query database
└── config/
    └── routes.php              # Route configuration (updated)

Documentation/
├── API_REPORT_DOCUMENTATION.md # Dokumentasi API lengkap
├── API_USAGE_EXAMPLES.md       # Contoh penggunaan berbagai bahasa
├── README_API_REPORT.md        # File ini
└── test_api_report.php         # File test API
```

## Installation

### 1. Upload Files
Upload file-file berikut ke direktori yang sesuai:

```bash
# Controller
application/controllers/Api_Report.php

# Model  
application/models/Api_Report_model.php

# Routes sudah diupdate otomatis
application/config/routes.php
```

### 2. Verify Dependencies
Pastikan library yang dibutuhkan sudah ada:
- `application/libraries/REST_Controller.php` ✅ (sudah ada)
- `application/models/Common_model.php` ✅ (sudah ada)

### 3. Test Installation
Jalankan test file untuk memastikan API berfungsi:

```bash
php test_api_report.php
```

Atau akses melalui browser:
```
http://your-domain.com/index.php/Api_Report/test_connection
```

## Configuration

### 1. Database Configuration
API menggunakan konfigurasi database yang sama dengan sistem utama. Pastikan file `application/config/database.php` sudah dikonfigurasi dengan benar.

### 2. Base URL Configuration
Pastikan `base_url` di `application/config/config.php` sudah benar:

```php
$config['base_url'] = 'http://your-domain.com/irestora/';
```

### 3. URL Routes
Route API sudah ditambahkan ke `application/config/routes.php`:

```php
// API Report Routes
$route['api/report/test'] = 'Api_Report/test_connection';
$route['api/report/daily'] = 'Api_Report/daily_summary';
$route['api/report/monthly'] = 'Api_Report/monthly_summary';
```

## API Endpoints

### 1. Test Connection
```
GET /api/report/test
GET /index.php/Api_Report/test_connection
```

### 2. Daily Summary Report
```
POST /api/report/daily
POST /index.php/Api_Report/daily_summary

Parameters:
- date (optional): Y-m-d format
- outlet_id (required): integer
- company_id (required): integer
```

### 3. Monthly Summary Report
```
POST /api/report/monthly
POST /index.php/Api_Report/monthly_summary

Parameters:
- month (optional): 1-12
- year (optional): YYYY
- outlet_id (required): integer
- company_id (required): integer
```

## Quick Start

### 1. Test Connection
```bash
curl http://your-domain.com/index.php/Api_Report/test_connection
```

### 2. Get Daily Report
```bash
curl -X POST http://your-domain.com/index.php/Api_Report/daily_summary \
  -d "date=2024-12-25&outlet_id=1&company_id=1"
```

### 3. Get Monthly Report
```bash
curl -X POST http://your-domain.com/index.php/Api_Report/monthly_summary \
  -d "month=12&year=2024&outlet_id=1&company_id=1"
```

## Integration Examples

### PHP Integration
```php
function getDailyReport($date, $outlet_id, $company_id) {
    $url = 'http://your-domain.com/index.php/Api_Report/daily_summary';
    
    $data = array(
        'date' => $date,
        'outlet_id' => $outlet_id,
        'company_id' => $company_id
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Usage
$report = getDailyReport('2024-12-25', 1, 1);
if ($report && $report['status']) {
    echo "Total Sales: " . $report['data']['summary']['sales']['total_amount'];
}
```

### JavaScript Integration
```javascript
async function getDailyReport(date, outletId, companyId) {
    const formData = new FormData();
    formData.append('date', date);
    formData.append('outlet_id', outletId);
    formData.append('company_id', companyId);
    
    const response = await fetch('/index.php/Api_Report/daily_summary', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// Usage
getDailyReport('2024-12-25', 1, 1).then(report => {
    if (report.status) {
        console.log('Total Sales:', report.data.summary.sales.total_amount);
    }
});
```

## Security Considerations

### 1. Current Security
- Validasi outlet_id dan company_id
- Input sanitization
- SQL injection protection melalui CodeIgniter Query Builder

### 2. Recommended Enhancements
Untuk production, disarankan menambahkan:

```php
// API Key Authentication
private function _validate_api_key() {
    $api_key = $this->input->get_request_header('X-API-Key');
    return $this->Api_Report_model->validateApiKey($api_key);
}

// Rate Limiting
private function _check_rate_limit() {
    $ip = $this->input->ip_address();
    return $this->Api_Report_model->checkRateLimit($ip);
}
```

## Troubleshooting

### 1. API Not Accessible
- Pastikan file controller dan model sudah diupload
- Cek konfigurasi base_url
- Pastikan web server bisa akses file PHP

### 2. Database Connection Error
- Cek konfigurasi database di `application/config/database.php`
- Pastikan user database punya akses ke tabel yang dibutuhkan

### 3. Empty Response
- Cek apakah outlet_id dan company_id valid
- Pastikan ada data di database untuk tanggal yang diminta

### 4. Permission Denied
- Cek file permissions (755 untuk direktori, 644 untuk file)
- Pastikan web server bisa akses direktori application

## Performance Optimization

### 1. Database Indexing
Pastikan index pada kolom yang sering diquery:

```sql
-- Recommended indexes
ALTER TABLE tbl_sales ADD INDEX idx_sale_date_outlet (sale_date, outlet_id);
ALTER TABLE tbl_purchase ADD INDEX idx_date_outlet (date, outlet_id);
ALTER TABLE tbl_expenses ADD INDEX idx_date_outlet (date, outlet_id);
```

### 2. Caching
Implementasi caching untuk laporan yang sering diakses:

```php
// Example caching implementation
$cache_key = "daily_report_{$date}_{$outlet_id}_{$company_id}";
$cached_data = $this->cache->get($cache_key);

if (!$cached_data) {
    $cached_data = $this->Api_Report_model->getDailySummaryReport($date, $outlet_id, $company_id);
    $this->cache->save($cache_key, $cached_data, 3600); // Cache 1 hour
}
```

## Support

Untuk pertanyaan atau issue terkait API ini:

1. Cek dokumentasi lengkap di `API_REPORT_DOCUMENTATION.md`
2. Lihat contoh penggunaan di `API_USAGE_EXAMPLES.md`
3. Jalankan test file `test_api_report.php`
4. Hubungi developer untuk support lebih lanjut

## Version History

- **v1.0.0** - Initial release
  - Daily summary report
  - Monthly summary report
  - Basic validation and error handling
  - Complete documentation and examples
