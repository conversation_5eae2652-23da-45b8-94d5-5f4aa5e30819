<?php
/*
  ###########################################################
  # PRODUCT NAME: 	iRestora PLUS - API Report Controller
  ###########################################################
  # AUTHER:		Custom Development
  ###########################################################
  # EMAIL:		<EMAIL>
  ###########################################################
  # COPYRIGHTS:		RESERVED BY Door Soft
  ###########################################################
  # WEBSITE:		http://www.doorsoft.co
  ###########################################################
  # This is API Report Controller for External System Integration
  ###########################################################
 */
defined('BASEPATH') OR exit('No direct script access allowed');
require(APPPATH.'libraries/REST_Controller.php');

class Api_Report extends REST_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Api_Report_model');
        $this->load->model('Common_model');
        $this->Common_model->setDefaultTimezone();
    }

    /**
     * API untuk laporan harian
     * @access public
     * @return json
     * @param POST: date (Y-m-d), outlet_id, company_id
     */
    public function daily_summary_post() {
        try {
            // Validasi input
            $date = $this->input->post('date');
            $outlet_id = $this->input->post('outlet_id');
            $company_id = $this->input->post('company_id');

            // Validasi required fields
            if (!$outlet_id || !$company_id) {
                $this->response([
                    'status' => false,
                    'message' => 'outlet_id dan company_id wajib diisi',
                    'data' => null
                ], REST_Controller::HTTP_BAD_REQUEST);
                return;
            }

            // Validasi format tanggal
            if ($date && !$this->_validate_date($date)) {
                $this->response([
                    'status' => false,
                    'message' => 'Format tanggal tidak valid. Gunakan format Y-m-d (contoh: 2024-12-25)',
                    'data' => null
                ], REST_Controller::HTTP_BAD_REQUEST);
                return;
            }

            // Jika tanggal tidak diisi, gunakan hari ini
            if (!$date) {
                $date = date('Y-m-d');
            }

            // Validasi outlet dan company
            if (!$this->_validate_outlet_company($outlet_id, $company_id)) {
                $this->response([
                    'status' => false,
                    'message' => 'outlet_id atau company_id tidak valid',
                    'data' => null
                ], REST_Controller::HTTP_UNAUTHORIZED);
                return;
            }

            // Ambil data laporan harian
            $report_data = $this->Api_Report_model->getDailySummaryReport($date, $outlet_id, $company_id);

            // Format response
            $response = [
                'status' => true,
                'message' => 'Laporan harian berhasil diambil',
                'data' => [
                    'report_date' => $date,
                    'outlet_id' => $outlet_id,
                    'company_id' => $company_id,
                    'outlet_name' => $this->_get_outlet_name($outlet_id),
                    'summary' => $report_data['summary'],
                    'details' => $report_data['details']
                ]
            ];

            $this->response($response, REST_Controller::HTTP_OK);

        } catch (Exception $e) {
            $this->response([
                'status' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'data' => null
            ], REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * API untuk laporan bulanan
     * @access public
     * @return json
     * @param POST: month (1-12), year (YYYY), outlet_id, company_id
     */
    public function monthly_summary_post() {
        try {
            // Validasi input
            $month = $this->input->post('month');
            $year = $this->input->post('year');
            $outlet_id = $this->input->post('outlet_id');
            $company_id = $this->input->post('company_id');

            // Validasi required fields
            if (!$outlet_id || !$company_id) {
                $this->response([
                    'status' => false,
                    'message' => 'outlet_id dan company_id wajib diisi',
                    'data' => null
                ], REST_Controller::HTTP_BAD_REQUEST);
                return;
            }

            // Jika bulan/tahun tidak diisi, gunakan bulan/tahun ini
            if (!$month) $month = date('n');
            if (!$year) $year = date('Y');

            // Validasi bulan dan tahun
            if (!$this->_validate_month_year($month, $year)) {
                $this->response([
                    'status' => false,
                    'message' => 'Bulan (1-12) atau tahun tidak valid',
                    'data' => null
                ], REST_Controller::HTTP_BAD_REQUEST);
                return;
            }

            // Validasi outlet dan company
            if (!$this->_validate_outlet_company($outlet_id, $company_id)) {
                $this->response([
                    'status' => false,
                    'message' => 'outlet_id atau company_id tidak valid',
                    'data' => null
                ], REST_Controller::HTTP_UNAUTHORIZED);
                return;
            }

            // Ambil data laporan bulanan
            $report_data = $this->Api_Report_model->getMonthlySummaryReport($month, $year, $outlet_id, $company_id);

            // Format response
            $response = [
                'status' => true,
                'message' => 'Laporan bulanan berhasil diambil',
                'data' => [
                    'report_month' => $month,
                    'report_year' => $year,
                    'month_name' => $this->_get_month_name($month),
                    'outlet_id' => $outlet_id,
                    'company_id' => $company_id,
                    'outlet_name' => $this->_get_outlet_name($outlet_id),
                    'summary' => $report_data['summary'],
                    'daily_breakdown' => $report_data['daily_breakdown']
                ]
            ];

            $this->response($response, REST_Controller::HTTP_OK);

        } catch (Exception $e) {
            $this->response([
                'status' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'data' => null
            ], REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validasi format tanggal
     */
    private function _validate_date($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Validasi bulan dan tahun
     */
    private function _validate_month_year($month, $year) {
        return (is_numeric($month) && $month >= 1 && $month <= 12) && 
               (is_numeric($year) && $year >= 2020 && $year <= 2030);
    }

    /**
     * Validasi outlet dan company
     */
    private function _validate_outlet_company($outlet_id, $company_id) {
        return $this->Api_Report_model->validateOutletCompany($outlet_id, $company_id);
    }

    /**
     * Ambil nama outlet
     */
    private function _get_outlet_name($outlet_id) {
        return $this->Api_Report_model->getOutletName($outlet_id);
    }

    /**
     * Ambil nama bulan
     */
    private function _get_month_name($month) {
        $months = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];
        return isset($months[$month]) ? $months[$month] : '';
    }

    /**
     * API untuk test koneksi
     * @access public
     * @return json
     */
    public function test_connection_get() {
        $this->response([
            'status' => true,
            'message' => 'API Report connection successful',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0'
        ], REST_Controller::HTTP_OK);
    }
}
