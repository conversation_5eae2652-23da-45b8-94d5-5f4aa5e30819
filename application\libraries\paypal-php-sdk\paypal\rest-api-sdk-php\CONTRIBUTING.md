## Contributing

* If you find solution to an [issue/improvements](https://github.com/paypal/PayPal-PHP-SDK/issues) in sdk that would be helpful to everyone, feel free to send us a pull request.
* The best help we could get from everyone is in writing more and more samples. We have a limited set of samples, and would appreciate if the community can help us write more and more of those, covering corner cases, that may be extremely useful to anyone using this SDK.
* The ideal approach to create a fix would be to fork the repository, create a branch in your repository, and make a pull request out of it.
* It is desirable if there is enough comments/documentation and Tests included in the pull request.
* For general idea of contribution, please follow the guidelines mentioned [here](https://guides.github.com/activities/contributing-to-open-source/).
