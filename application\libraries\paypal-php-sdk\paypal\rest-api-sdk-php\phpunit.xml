<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="./tests/bootstrap.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         syntaxCheck="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         testSuiteLoaderClass="PHPUnit_Runner_StandardTestSuiteLoader">

    <testsuites>
        <testsuite name="All">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <groups>
        <exclude>
            <group>integration</group>
        </exclude>
    </groups>

    <logging>
        <log type="junit" target="build/junit.xml" logIncompleteSkipped="false" />
        <log type="coverage-clover" target="build/coverage/clover.xml"/>
    </logging>

    <filter>
        <whitelist>
            <directory suffix=".php">./lib</directory>
            <exclude>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
