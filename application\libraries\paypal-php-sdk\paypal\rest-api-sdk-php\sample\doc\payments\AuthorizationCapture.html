<!DOCTYPE html><html lang="en"><head><title>payments/AuthorizationCapture</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/AuthorizationCapture"><meta name="groc-project-path" content="payments/AuthorizationCapture.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/AuthorizationCapture.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="authorizationcapture">AuthorizationCapture</h1>
<p>This sample code demonstrates how you can capture 
a previously authorized payment.
API used: /v1/payments/payment
<a href="https://developer.paypal.com/webapps/developer/docs/api/#capture-an-authorization">https://developer.paypal.com/webapps/developer/docs/api/#capture-an-authorization</a></p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Authorization $authorization */</span>
<span class="hljs-variable">$authorization</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetAuthorization.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Amount</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Authorization</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Capture</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="capture-payment">Capture Payment</h3>
<p>You can capture and process a previously created authorization
by invoking the $authorization-&gt;capture method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$authId</span> = <span class="hljs-variable">$authorization</span>-&gt;getId();

    <span class="hljs-variable">$amt</span> = <span class="hljs-keyword">new</span> Amount();
    <span class="hljs-variable">$amt</span>-&gt;setCurrency(<span class="hljs-string">"USD"</span>)
        -&gt;setTotal(<span class="hljs-number">1</span>);

    <span class="hljs-comment">### Capture</span>
    <span class="hljs-variable">$capture</span> = <span class="hljs-keyword">new</span> Capture();
    <span class="hljs-variable">$capture</span>-&gt;setAmount(<span class="hljs-variable">$amt</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Perform a capture</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$getCapture</span> = <span class="hljs-variable">$authorization</span>-&gt;capture(<span class="hljs-variable">$capture</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Capture Payment"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$capture</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Capture Payment"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-variable">$getCapture</span>-&gt;getId(), <span class="hljs-variable">$capture</span>, <span class="hljs-variable">$getCapture</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$getCapture</span>;</div></div></div></div></body></html>
