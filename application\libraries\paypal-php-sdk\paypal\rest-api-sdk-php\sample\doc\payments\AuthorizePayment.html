<!DOCTYPE html><html lang="en"><head><title>payments/AuthorizePayment</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/AuthorizePayment"><meta name="groc-project-path" content="payments/AuthorizePayment.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/AuthorizePayment.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="authorize-payment">Authorize Payment</h1>
<p>This sample code demonstrates how you can authorize a payment.
API used: /v1/payments/authorization
<a href="https://developer.paypal.com/webapps/developer/docs/integration/direct/capture-payment/#authorize-the-payment">https://developer.paypal.com/webapps/developer/docs/integration/direct/capture-payment/#authorize-the-payment</a></p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Address</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Amount</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">FundingInstrument</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payer</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">PaymentCard</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Transaction</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The biggest difference between creating a payment, and authorizing a payment is to set the intent of payment
to correct setting. In this case, it would be &#39;authorize&#39;</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$addr</span> = <span class="hljs-keyword">new</span> Address();
<span class="hljs-variable">$addr</span>-&gt;setLine1(<span class="hljs-string">"3909 Witmer Road"</span>)
    -&gt;setLine2(<span class="hljs-string">"Niagara Falls"</span>)
    -&gt;setCity(<span class="hljs-string">"Niagara Falls"</span>)
    -&gt;setState(<span class="hljs-string">"NY"</span>)
    -&gt;setPostalCode(<span class="hljs-string">"14305"</span>)
    -&gt;setCountryCode(<span class="hljs-string">"US"</span>)
    -&gt;setPhone(<span class="hljs-string">"************"</span>);

<span class="hljs-variable">$paymentCard</span> = <span class="hljs-keyword">new</span> PaymentCard();
<span class="hljs-variable">$paymentCard</span>-&gt;setType(<span class="hljs-string">"visa"</span>)
    -&gt;setNumber(<span class="hljs-string">"****************"</span>)
    -&gt;setExpireMonth(<span class="hljs-string">"11"</span>)
    -&gt;setExpireYear(<span class="hljs-string">"2019"</span>)
    -&gt;setCvv2(<span class="hljs-string">"012"</span>)
    -&gt;setFirstName(<span class="hljs-string">"Joe"</span>)
    -&gt;setLastName(<span class="hljs-string">"Shopper"</span>)
    -&gt;setBillingCountry(<span class="hljs-string">"US"</span>)
    -&gt;setBillingAddress(<span class="hljs-variable">$addr</span>);

<span class="hljs-variable">$fi</span> = <span class="hljs-keyword">new</span> FundingInstrument();
<span class="hljs-variable">$fi</span>-&gt;setPaymentCard(<span class="hljs-variable">$paymentCard</span>);

<span class="hljs-variable">$payer</span> = <span class="hljs-keyword">new</span> Payer();
<span class="hljs-variable">$payer</span>-&gt;setPaymentMethod(<span class="hljs-string">"credit_card"</span>)
    -&gt;setFundingInstruments(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$fi</span>));

<span class="hljs-variable">$amount</span> = <span class="hljs-keyword">new</span> Amount();
<span class="hljs-variable">$amount</span>-&gt;setCurrency(<span class="hljs-string">"USD"</span>)
    -&gt;setTotal(<span class="hljs-number">1</span>);

<span class="hljs-variable">$transaction</span> = <span class="hljs-keyword">new</span> Transaction();
<span class="hljs-variable">$transaction</span>-&gt;setAmount(<span class="hljs-variable">$amount</span>)
    -&gt;setDescription(<span class="hljs-string">"Payment description."</span>);

<span class="hljs-variable">$payment</span> = <span class="hljs-keyword">new</span> Payment();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Setting intent to authorize creates a payment
authorization. Setting it to sale creates actual payment</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payment</span>-&gt;setIntent(<span class="hljs-string">"authorize"</span>)
    -&gt;setPayer(<span class="hljs-variable">$payer</span>)
    -&gt;setTransactions(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$transaction</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$payment</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-payment">Create Payment</h3>
<p>Create a payment by calling the payment-&gt;create() method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)
The return object contains the state.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$payment</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">'Authorize a Payment'</span>, <span class="hljs-string">'Authorized Payment'</span>, <span class="hljs-variable">$payment</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">'Authorize a Payment'</span>, <span class="hljs-string">'Authorized Payment'</span>, <span class="hljs-variable">$payment</span>-&gt;getId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$payment</span>);

<span class="hljs-variable">$transactions</span> = <span class="hljs-variable">$payment</span>-&gt;getTransactions();
<span class="hljs-variable">$relatedResources</span> = <span class="hljs-variable">$transactions</span>[<span class="hljs-number">0</span>]-&gt;getRelatedResources();
<span class="hljs-variable">$authorization</span> = <span class="hljs-variable">$relatedResources</span>[<span class="hljs-number">0</span>]-&gt;getAuthorization();

<span class="hljs-keyword">return</span> <span class="hljs-variable">$authorization</span>;</div></div></div></div></body></html>
