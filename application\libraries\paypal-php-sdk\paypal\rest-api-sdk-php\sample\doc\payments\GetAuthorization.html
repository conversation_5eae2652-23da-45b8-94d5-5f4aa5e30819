<!DOCTYPE html><html lang="en"><head><title>payments/GetAuthorization</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/GetAuthorization"><meta name="groc-project-path" content="payments/GetAuthorization.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/GetAuthorization.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="getauthorization">GetAuthorization</h1>
<p>This sample code demonstrates how you can get details 
of an authorized payment.
API used: /v1/payments/authorization/&lt;$authorizationId&gt;</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Authorization $authorization */</span>
<span class="hljs-variable">$authorization</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'AuthorizePayment.php'</span>;
<span class="hljs-variable">$authorizationId</span> = <span class="hljs-variable">$authorization</span>-&gt;getId();
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Authorization</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="getauthorization">GetAuthorization</h3>
<p>You can retrieve info about an Authorization
by invoking the Authorization::get method
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)
The return object contains the authorization state.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Retrieve the authorization</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$result</span> = Authorization::get(<span class="hljs-variable">$authorizationId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Authorization"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Authorization"</span>, <span class="hljs-string">"Authorization"</span>, <span class="hljs-variable">$authorizationId</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$result</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$result</span>;</div></div></div></div></body></html>
