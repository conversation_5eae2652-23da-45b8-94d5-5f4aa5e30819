<!DOCTYPE html><html lang="en"><head><title>payments/GetCapture</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/GetCapture"><meta name="groc-project-path" content="payments/GetCapture.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/GetCapture.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="getcapture">GetCapture</h1>
<p>This sample code demonstrates how you can lookup the details 
of a captured payment.
API used: /v1/payments/capture/&lt;$captureId&gt;</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Capture $request */</span>
<span class="hljs-variable">$request</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'AuthorizationCapture.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Capture</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-capture-details">Retrieve Capture details</h3>
<p>You can look up a capture by invoking the Capture::get method 
with a valid ApiContext (See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$capture</span> = Capture::get(<span class="hljs-variable">$request</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Captured Payment"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-variable">$request</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Captured Payment"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-variable">$capture</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$capture</span>);</div></div></div></div></body></html>
