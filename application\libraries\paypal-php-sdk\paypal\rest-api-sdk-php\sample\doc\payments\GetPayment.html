<!DOCTYPE html><html lang="en"><head><title>payments/GetPayment</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/GetPayment"><meta name="groc-project-path" content="payments/GetPayment.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/GetPayment.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="getpaymentsample">GetPaymentSample</h1>
<p>This sample code demonstrate how you can
retrieve a list of all Payment resources
you&#39;ve created using the Payments API.
Note various query parameters that you can
use to filter, and paginate through the
payments list.
API used: GET /v1/payments/payments</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Payment $createdPayment */</span>
<span class="hljs-variable">$createdPayment</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePayment.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;

<span class="hljs-variable">$paymentId</span> = <span class="hljs-variable">$createdPayment</span>-&gt;getId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-payment">Retrieve payment</h3>
<p>Retrieve the payment object by calling the
static <code>get</code> method
on the Payment class by passing a valid
Payment ID
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$payment</span> = Payment::get(<span class="hljs-variable">$paymentId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Payment"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Payment"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-variable">$payment</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$payment</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$payment</span>;</div></div></div></div></body></html>
