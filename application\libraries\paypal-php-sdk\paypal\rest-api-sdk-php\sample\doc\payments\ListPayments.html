<!DOCTYPE html><html lang="en"><head><title>payments/ListPayments</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/ListPayments"><meta name="groc-project-path" content="payments/ListPayments.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/ListPayments.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="getpaymentlist">GetPaymentList</h1>
<p>This sample code demonstrate how you can
retrieve a list of all Payment resources
you&#39;ve created using the Payments API.
Note various query parameters that you can
use to filter, and paginate through the
payments list.
API used: GET /v1/payments/payments</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-string">'CreatePayment.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-payment">Retrieve payment</h3>
<p>Retrieve the PaymentHistory object by calling the
static <code>get</code> method on the Payment class, 
and pass a Map object that contains
query parameters for paginations and filtering.
Refer the method doc for valid values for keys
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(<span class="hljs-string">'count'</span> =&gt; <span class="hljs-number">10</span>, <span class="hljs-string">'start_index'</span> =&gt; <span class="hljs-number">5</span>);

    <span class="hljs-variable">$payments</span> = Payment::all(<span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"List Payments"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"List Payments"</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$payments</span>);</div></div></div></div></body></html>
