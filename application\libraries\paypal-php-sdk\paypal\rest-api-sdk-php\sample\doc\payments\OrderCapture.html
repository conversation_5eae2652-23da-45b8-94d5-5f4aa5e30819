<!DOCTYPE html><html lang="en"><head><title>payments/OrderCapture</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/OrderCapture"><meta name="groc-project-path" content="payments/OrderCapture.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/OrderCapture.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="capture-order-sample">Capture Order Sample</h1>
<p>To authorize an order payment, pass the orderId in the URI of a POST call. This begins the process of confirming that funds are available until it is time to complete the payment transaction.
API used: POST /v1/payments/orders/<Order-Id>/authorize</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\Payment $payment */</span>
<span class="hljs-variable">$payment</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/ExecutePayment.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Amount</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Capture</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="approval-status">Approval Status</h3>
<p>Determine if the user approved the payment or not</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">if</span> (<span class="hljs-keyword">isset</span>(<span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>]) &amp;&amp; <span class="hljs-variable">$_GET</span>[<span class="hljs-string">'success'</span>] == <span class="hljs-string">'true'</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-the-order">Retrieve the order</h3>
<p>OrderId could be retrieved by parsing the object inside related_resources.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$transactions</span> = <span class="hljs-variable">$payment</span>-&gt;getTransactions();
    <span class="hljs-variable">$transaction</span> = <span class="hljs-variable">$transactions</span>[<span class="hljs-number">0</span>];
    <span class="hljs-variable">$relatedResources</span> = <span class="hljs-variable">$transaction</span>-&gt;getRelatedResources();
    <span class="hljs-variable">$relatedResource</span> = <span class="hljs-variable">$relatedResources</span>[<span class="hljs-number">0</span>];
    <span class="hljs-variable">$order</span> = <span class="hljs-variable">$relatedResource</span>-&gt;getOrder();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-capture-object">Create Capture Object</h3>
<p>with Amount in it</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$capture</span> = <span class="hljs-keyword">new</span> Capture();
    <span class="hljs-variable">$capture</span>-&gt;setIsFinalCapture(<span class="hljs-keyword">true</span>);
    <span class="hljs-variable">$capture</span>-&gt;setAmount(<span class="hljs-keyword">new</span> Amount(
        <span class="hljs-string">'{
            "total": "2.00",
            "currency": "USD"
        }'</span>
    ));

    <span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="capture-order">Capture Order</h3>
<p>Capture the order by passing capture object we created.
We will get a new capture object back.</p></div></div><div class="code"><div class="wrapper">        <span class="hljs-variable">$result</span> = <span class="hljs-variable">$order</span>-&gt;capture(<span class="hljs-variable">$capture</span>, <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printResult(<span class="hljs-string">"Captured Order"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-variable">$result</span>-&gt;getId(), <span class="hljs-variable">$capture</span>, <span class="hljs-variable">$result</span>);
    } <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printError(<span class="hljs-string">"Captured Order"</span>, <span class="hljs-string">"Capture"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$capture</span>, <span class="hljs-variable">$ex</span>);
        <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
    }

    <span class="hljs-keyword">return</span> <span class="hljs-variable">$result</span>;
} <span class="hljs-keyword">else</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printResult(<span class="hljs-string">"User Cancelled the Approval"</span>, <span class="hljs-keyword">null</span>);
    <span class="hljs-keyword">exit</span>;
}</div></div></div></div></body></html>
