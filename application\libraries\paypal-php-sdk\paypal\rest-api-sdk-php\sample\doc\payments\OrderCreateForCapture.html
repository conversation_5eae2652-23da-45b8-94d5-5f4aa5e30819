<!DOCTYPE html><html lang="en"><head><title>payments/OrderCreateForCapture</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payments/OrderCreateForCapture"><meta name="groc-project-path" content="payments/OrderCreateForCapture.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payments/OrderCreateForCapture.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="order-create-using-paypal">Order Create Using PayPal</h1>
<p>In a call to the /payment resource, provide the payment details. In the intent field, specify order, and set the payment_method to paypal. Creating an order is similar to creating a payment.
API used: /v1/payments/payment</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Amount</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Details</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Item</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">ItemList</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payer</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">RedirectUrls</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Transaction</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="payer">Payer</h3>
<p>A resource representing a Payer that funds a payment
For paypal account payments, set payment method
to &#39;paypal&#39;.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payer</span> = <span class="hljs-keyword">new</span> Payer();
<span class="hljs-variable">$payer</span>-&gt;setPaymentMethod(<span class="hljs-string">"paypal"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="itemized-information">Itemized information</h3>
<p>(Optional) Lets you specify item wise
information</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$item1</span> = <span class="hljs-keyword">new</span> Item();
<span class="hljs-variable">$item1</span>-&gt;setName(<span class="hljs-string">'Ground Coffee 40 oz'</span>)
    -&gt;setCurrency(<span class="hljs-string">'USD'</span>)
    -&gt;setQuantity(<span class="hljs-number">1</span>)
    -&gt;setPrice(<span class="hljs-number">7.5</span>);
<span class="hljs-variable">$item2</span> = <span class="hljs-keyword">new</span> Item();
<span class="hljs-variable">$item2</span>-&gt;setName(<span class="hljs-string">'Granola bars'</span>)
    -&gt;setCurrency(<span class="hljs-string">'USD'</span>)
    -&gt;setQuantity(<span class="hljs-number">5</span>)
    -&gt;setPrice(<span class="hljs-number">2</span>);

<span class="hljs-variable">$itemList</span> = <span class="hljs-keyword">new</span> ItemList();
<span class="hljs-variable">$itemList</span>-&gt;setItems(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$item1</span>, <span class="hljs-variable">$item2</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="additional-payment-details">Additional payment details</h3>
<p>Use this optional field to set additional
payment information such as tax, shipping
charges etc.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$details</span> = <span class="hljs-keyword">new</span> Details();
<span class="hljs-variable">$details</span>-&gt;setShipping(<span class="hljs-number">1.2</span>)
    -&gt;setTax(<span class="hljs-number">1.3</span>)
    -&gt;setSubtotal(<span class="hljs-number">17.50</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="amount">Amount</h3>
<p>Lets you specify a payment amount.
You can also specify additional details
such as shipping, tax.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$amount</span> = <span class="hljs-keyword">new</span> Amount();
<span class="hljs-variable">$amount</span>-&gt;setCurrency(<span class="hljs-string">"USD"</span>)
    -&gt;setTotal(<span class="hljs-number">20</span>)
    -&gt;setDetails(<span class="hljs-variable">$details</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="transaction">Transaction</h3>
<p>A transaction defines the contract of a
payment - what is the payment for and who
is fulfilling it. </p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$transaction</span> = <span class="hljs-keyword">new</span> Transaction();
<span class="hljs-variable">$transaction</span>-&gt;setAmount(<span class="hljs-variable">$amount</span>)
    -&gt;setItemList(<span class="hljs-variable">$itemList</span>)
    -&gt;setDescription(<span class="hljs-string">"Payment description"</span>)
    -&gt;setInvoiceNumber(uniqid());</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="redirect-urls">Redirect urls</h3>
<p>Set the urls that the buyer must be redirected to after 
payment approval/ cancellation.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$baseUrl</span> = getBaseUrl();
<span class="hljs-variable">$redirectUrls</span> = <span class="hljs-keyword">new</span> RedirectUrls();
<span class="hljs-variable">$redirectUrls</span>-&gt;setReturnUrl(<span class="hljs-string">"$baseUrl/OrderCapture.php?success=true"</span>)
    -&gt;setCancelUrl(<span class="hljs-string">"$baseUrl/OrderCapture.php?success=false"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="payment">Payment</h3>
<p>A Payment Resource; create one using
the above types and intent set to &#39;order&#39;</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payment</span> = <span class="hljs-keyword">new</span> Payment();
<span class="hljs-variable">$payment</span>-&gt;setIntent(<span class="hljs-string">"order"</span>)
    -&gt;setPayer(<span class="hljs-variable">$payer</span>)
    -&gt;setRedirectUrls(<span class="hljs-variable">$redirectUrls</span>)
    -&gt;setTransactions(<span class="hljs-keyword">array</span>(<span class="hljs-variable">$transaction</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$payment</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-payment">Create Payment</h3>
<p>Create a payment by calling the &#39;create&#39; method
passing it a valid apiContext.
(See bootstrap.php for more on <code>ApiContext</code>)
The return object contains the state and the
url to which the buyer must be redirected to
for payment approval</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$payment</span>-&gt;create(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Created Payment Order Using PayPal. Please visit the URL to Approve."</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-redirect-url">Get redirect url</h3>
<p>The API response provides the url that you must redirect
the buyer to. Retrieve the url from the $payment-&gt;getApprovalLink()
method</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$approvalUrl</span> = <span class="hljs-variable">$payment</span>-&gt;getApprovalLink();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Created Payment Order Using PayPal. Please visit the URL to Approve."</span>, <span class="hljs-string">"Payment"</span>, <span class="hljs-string">"&lt;a href='$approvalUrl' &gt;$approvalUrl&lt;/a&gt;"</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$payment</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$payment</span>;</div></div></div></div></body></html>
