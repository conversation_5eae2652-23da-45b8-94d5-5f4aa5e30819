<!DOCTYPE html><html lang="en"><head><title>payouts/CancelPayoutItem</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payouts/CancelPayoutItem"><meta name="groc-project-path" content="payouts/CancelPayoutItem.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payouts/CancelPayoutItem.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="cancel-payout-item-status-sample">Cancel Payout Item Status Sample</h1>
<p>Use this call to cancel an existing, unclaimed transaction. If an unclaimed item is not claimed within 30 days, the funds will be automatically returned to the sender. This call can be used to cancel the unclaimed item prior to the automatic 30-day return.
<a href="https://developer.paypal.com/docs/api/#cancel-an-unclaimed-payout-item">https://developer.paypal.com/docs/api/#cancel-an-unclaimed-payout-item</a>
API used: POST /v1/payments/payouts-item/<Payout-Item-Id>/cancel</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\PayoutBatch $payoutBatch */</span>
<span class="hljs-variable">$payoutBatch</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateSinglePayout.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="payout-item-id">Payout Item ID</h2>
<p>You can replace this with your Payout Batch Id on already created Payout.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payoutItems</span> = <span class="hljs-variable">$payoutBatch</span>-&gt;getItems();
<span class="hljs-variable">$payoutItem</span> = <span class="hljs-variable">$payoutItems</span>[<span class="hljs-number">0</span>];
<span class="hljs-variable">$payoutItemId</span> = <span class="hljs-variable">$payoutItem</span>-&gt;getPayoutItemId();

<span class="hljs-variable">$output</span> = <span class="hljs-keyword">null</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="cancel-payout-item">Cancel Payout Item</h3>
<p>Check if Payout Item is UNCLAIMED, and if so, cancel it.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">if</span> (<span class="hljs-variable">$payoutItem</span>-&gt;getTransactionStatus() == <span class="hljs-string">'UNCLAIMED'</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Cancel the Payout Item</p></div></div><div class="code"><div class="wrapper">        <span class="hljs-variable">$output</span> = \PayPal\Api\PayoutItem::cancel(<span class="hljs-variable">$payoutItemId</span>, <span class="hljs-variable">$apiContext</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printResult(<span class="hljs-string">"Cancel Unclaimed Payout Item"</span>, <span class="hljs-string">"PayoutItem"</span>, <span class="hljs-variable">$output</span>-&gt;getPayoutItemId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);
    } <span class="hljs-keyword">else</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The item transaction status is not unclaimed. You can only cancel an unclaimed transaction.
NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">        ResultPrinter::printError(<span class="hljs-string">"Cancel Unclaimed Payout Item"</span>, <span class="hljs-string">"PayoutItem"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$payoutItemId</span>, <span class="hljs-keyword">new</span> <span class="hljs-keyword">Exception</span>(<span class="hljs-string">"Payout Item Status is not UNCLAIMED"</span>));
    }
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Cancel Unclaimed Payout Item"</span>, <span class="hljs-string">"PayoutItem"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$payoutItemId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
