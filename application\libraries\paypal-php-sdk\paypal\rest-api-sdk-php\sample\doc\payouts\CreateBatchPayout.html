<!DOCTYPE html><html lang="en"><head><title>payouts/CreateBatchPayout</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payouts/CreateBatchPayout"><meta name="groc-project-path" content="payouts/CreateBatchPayout.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payouts/CreateBatchPayout.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="create-bulk-payout-sample">Create Bulk Payout Sample</h1>
<p>This sample code demonstrate how you can create a synchronous payout sample, as documented here at:
<a href="https://developer.paypal.com/docs/integration/direct/create-batch-payout/">https://developer.paypal.com/docs/integration/direct/create-batch-payout/</a>
API used: /v1/payments/payouts</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../bootstrap.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Create a new instance of Payout object</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payouts</span> = <span class="hljs-keyword">new</span> \PayPal\Api\Payout();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>This is how our body should look like:</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/*
 *{
    "sender_batch_header": {
        "sender_batch_id": "random_uniq_id",
        "email_subject": "You have a payment"
    },
    "items": [
        {
            "recipient_type": "EMAIL",
            "amount": {
                "value": 0.99,
                "currency": "USD"
            },
            "receiver": "shirt-supplier-one<span class="hljs-doctag">@mail</span>.com",
            "note": "Thank you.",
            "sender_item_id": "item_1"
        },
        {
            "recipient_type": "EMAIL",
            "amount": {
                "value": 0.90,
                "currency": "USD"
            },
            "receiver": "shirt-supplier-two<span class="hljs-doctag">@mail</span>.com",
            "note": "Thank you.",
            "sender_item_id": "item_2"
        },
        {
            "recipient_type": "EMAIL",
            "amount": {
                "value": 2.00,
                "currency": "USD"
            },
            "receiver": "shirt-supplier-three<span class="hljs-doctag">@mail</span>.com",
            "note": "Thank you.",
            "sender_item_id": "item_3"
        }
    ]
}
 */</span>

<span class="hljs-variable">$senderBatchHeader</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PayoutSenderBatchHeader();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="note">NOTE:</h3>
<p>You can prevent duplicate batches from being processed. If you specify a <code>sender_batch_id</code> that was used in the last 30 days, the batch will not be processed. For items, you can specify a <code>sender_item_id</code>. If the value for the <code>sender_item_id</code> is a duplicate of a payout item that was processed in the last 30 days, the item will not be processed.</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="batch-header-instance">Batch Header Instance</h4></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-variable">$senderBatchHeader</span>-&gt;setSenderBatchId(uniqid())
    -&gt;setEmailSubject(<span class="hljs-string">"You have a payment"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="sender-item">Sender Item</h4>
<p>Please note that if you are using single payout with sync mode, you can only pass one Item in the request</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$senderItem1</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PayoutItem();
<span class="hljs-variable">$senderItem1</span>-&gt;setRecipientType(<span class="hljs-string">'Email'</span>)
    -&gt;setNote(<span class="hljs-string">'Thanks you.'</span>)
    -&gt;setReceiver(<span class="hljs-string">'<EMAIL>'</span>)
    -&gt;setSenderItemId(<span class="hljs-string">"item_1"</span> . uniqid())
    -&gt;setAmount(<span class="hljs-keyword">new</span> \PayPal\Api\Currency(<span class="hljs-string">'{
                        "value":"0.99",
                        "currency":"USD"
                    }'</span>));</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="sender-item-2">Sender Item 2</h4>
<p>There are many different ways of assigning values in PayPal SDK. Here is another way where you could directly inject json string.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$senderItem2</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PayoutItem(
    <span class="hljs-string">'{
            "recipient_type": "EMAIL",
            "amount": {
                "value": 0.90,
                "currency": "USD"
            },
            "receiver": "<EMAIL>",
            "note": "Thank you.",
            "sender_item_id": "item_2"
        }'</span>
);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h4 id="sender-item-3">Sender Item 3</h4>
<p>One more way of assigning values in constructor when creating instance of PayPalModel object. Injecting array.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$senderItem3</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PayoutItem(
    <span class="hljs-keyword">array</span>(
        <span class="hljs-string">"recipient_type"</span> =&gt; <span class="hljs-string">"EMAIL"</span>,
        <span class="hljs-string">"receiver"</span> =&gt; <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-string">"note"</span> =&gt; <span class="hljs-string">"Thank you."</span>,
        <span class="hljs-string">"sender_item_id"</span> =&gt; uniqid(),
        <span class="hljs-string">"amount"</span> =&gt; <span class="hljs-keyword">array</span>(
            <span class="hljs-string">"value"</span> =&gt; <span class="hljs-string">"0.90"</span>,
            <span class="hljs-string">"currency"</span> =&gt; <span class="hljs-string">"USD"</span>
        )

    )
);

<span class="hljs-variable">$payouts</span>-&gt;setSenderBatchHeader(<span class="hljs-variable">$senderBatchHeader</span>)
    -&gt;addItem(<span class="hljs-variable">$senderItem1</span>)-&gt;addItem(<span class="hljs-variable">$senderItem2</span>)-&gt;addItem(<span class="hljs-variable">$senderItem3</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>For Sample Purposes Only.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$request</span> = <span class="hljs-keyword">clone</span> <span class="hljs-variable">$payouts</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="create-payout">Create Payout</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = <span class="hljs-variable">$payouts</span>-&gt;create(<span class="hljs-keyword">null</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Created Batch Payout"</span>, <span class="hljs-string">"Payout"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$request</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Created Batch Payout"</span>, <span class="hljs-string">"Payout"</span>, <span class="hljs-variable">$output</span>-&gt;getBatchHeader()-&gt;getPayoutBatchId(), <span class="hljs-variable">$request</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
