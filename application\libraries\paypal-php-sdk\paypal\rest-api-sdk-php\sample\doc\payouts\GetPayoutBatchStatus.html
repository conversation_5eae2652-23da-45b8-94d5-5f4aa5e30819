<!DOCTYPE html><html lang="en"><head><title>payouts/GetPayoutBatchStatus</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payouts/GetPayoutBatchStatus"><meta name="groc-project-path" content="payouts/GetPayoutBatchStatus.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payouts/GetPayoutBatchStatus.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-payout-batch-status-sample">Get Payout Batch Status Sample</h1>
<p>This sample code demonstrate how you can get the batch payout status of a created batch payout, as documented here at:
<a href="https://developer.paypal.com/docs/api/#get-the-status-of-a-batch-payout">https://developer.paypal.com/docs/api/#get-the-status-of-a-batch-payout</a>
API used: GET /v1/payments/payouts/<Payout-Batch-Id></p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\PayoutBatch $payoutBatch */</span>
<span class="hljs-variable">$payoutBatch</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateBatchPayout.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="payout-batch-id">Payout Batch ID</h2>
<p>You can replace this with your Payout Batch Id on already created Payout.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payoutBatchId</span> = <span class="hljs-variable">$payoutBatch</span>-&gt;getBatchHeader()-&gt;getPayoutBatchId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-payout-batch-status">Get Payout Batch Status</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\Payout::get(<span class="hljs-variable">$payoutBatchId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Payout Batch Status"</span>, <span class="hljs-string">"PayoutBatch"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$payoutBatchId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Payout Batch Status"</span>, <span class="hljs-string">"PayoutBatch"</span>, <span class="hljs-variable">$output</span>-&gt;getBatchHeader()-&gt;getPayoutBatchId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
