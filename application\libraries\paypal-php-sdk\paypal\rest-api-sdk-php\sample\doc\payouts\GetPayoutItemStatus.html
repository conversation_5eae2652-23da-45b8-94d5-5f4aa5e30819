<!DOCTYPE html><html lang="en"><head><title>payouts/GetPayoutItemStatus</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="payouts/GetPayoutItemStatus"><meta name="groc-project-path" content="payouts/GetPayoutItemStatus.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">payouts/GetPayoutItemStatus.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-payout-item-status-sample">Get Payout Item Status Sample</h1>
<p>Use this call to get data about a payout item, including the status, without retrieving an entire batch. You can get the status of an individual payout item in a batch in order to review the current status of a previously-unclaimed, or pending, payout item.
<a href="https://developer.paypal.com/docs/api/#get-the-status-of-a-payout-item">https://developer.paypal.com/docs/api/#get-the-status-of-a-payout-item</a>
API used: GET /v1/payments/payouts-item/<Payout-Item-Id></p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\PayoutBatch $payoutBatch */</span>
<span class="hljs-variable">$payoutBatch</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'GetPayoutBatchStatus.php'</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h2 id="payout-item-id">Payout Item ID</h2>
<p>You can replace this with your Payout Batch Id on already created Payout.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$payoutItems</span> = <span class="hljs-variable">$payoutBatch</span>-&gt;getItems();
<span class="hljs-variable">$payoutItem</span> = <span class="hljs-variable">$payoutItems</span>[<span class="hljs-number">0</span>];
<span class="hljs-variable">$payoutItemId</span> = <span class="hljs-variable">$payoutItem</span>-&gt;getPayoutItemId();</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-payout-item-status">Get Payout Item Status</h3></div></div></div><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$output</span> = \PayPal\Api\PayoutItem::get(<span class="hljs-variable">$payoutItemId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Payout Item Status"</span>, <span class="hljs-string">"PayoutItem"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$payoutItemId</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Payout Item Status"</span>, <span class="hljs-string">"PayoutItem"</span>, <span class="hljs-variable">$output</span>-&gt;getPayoutItemId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$output</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$output</span>;</div></div></div></div></body></html>
