<!DOCTYPE html><html lang="en"><head><title>sale/GetSale</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="sale/GetSale"><meta name="groc-project-path" content="sale/GetSale.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">sale/GetSale.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-sale-sample">Get Sale sample</h1>
<p>Sale transactions are nothing but completed payments.
This sample code demonstrates how you can retrieve 
details of completed Sale Transaction.
API used: /v1/payments/sale/{sale-id}</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> Payment $payment */</span>
<span class="hljs-variable">$payment</span> = <span class="hljs-keyword">require</span> <span class="hljs-keyword">__DIR__</span> . <span class="hljs-string">'/../payments/CreatePayment.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Payment</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Sale</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="get-sale-from-created-payment">Get Sale From Created Payment</h3>
<p>You can retrieve the sale Id from Related Resources for each transactions.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$transactions</span> = <span class="hljs-variable">$payment</span>-&gt;getTransactions();
<span class="hljs-variable">$relatedResources</span> = <span class="hljs-variable">$transactions</span>[<span class="hljs-number">0</span>]-&gt;getRelatedResources();
<span class="hljs-variable">$sale</span> = <span class="hljs-variable">$relatedResources</span>[<span class="hljs-number">0</span>]-&gt;getSale();
<span class="hljs-variable">$saleId</span> = <span class="hljs-variable">$sale</span>-&gt;getId();

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="retrieve-the-sale-object">Retrieve the sale object</h3>
<p>Pass the ID of the sale
transaction from your payment resource.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$sale</span> = Sale::get(<span class="hljs-variable">$saleId</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Look Up A Sale"</span>, <span class="hljs-string">"Sale"</span>, <span class="hljs-variable">$sale</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Look Up A Sale"</span>, <span class="hljs-string">"Sale"</span>, <span class="hljs-variable">$sale</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$sale</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$sale</span>;</div></div></div></div></body></html>
