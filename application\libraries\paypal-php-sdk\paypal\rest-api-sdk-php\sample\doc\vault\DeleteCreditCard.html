<!DOCTYPE html><html lang="en"><head><title>vault/DeleteCreditCard</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="vault/DeleteCreditCard"><meta name="groc-project-path" content="vault/DeleteCreditCard.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">vault/DeleteCreditCard.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="delete-creditcard-sample">Delete CreditCard Sample</h1>
<p>This sample code demonstrate how you can
delete a saved credit card.
API used: /v1/vault/credit-card/{<creditCardId>}
NOTE: HTTP method used here is DELETE</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> CreditCard $card */</span>
<span class="hljs-variable">$card</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateCreditCard.php'</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">CreditCard</span>;

<span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="delete-card">Delete Card</h3>
<p>Lookup and delete a saved credit card.
(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$card</span>-&gt;delete(<span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Delete Credit Card"</span>, <span class="hljs-string">"Credit Card"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Delete Credit Card"</span>, <span class="hljs-string">"Credit Card"</span>, <span class="hljs-variable">$card</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-keyword">null</span>);</div></div></div></div></body></html>
