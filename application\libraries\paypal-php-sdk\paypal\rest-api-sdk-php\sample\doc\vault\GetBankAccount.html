<!DOCTYPE html><html lang="en"><head><title>vault/GetBankAccount</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="vault/GetBankAccount"><meta name="groc-project-path" content="vault/GetBankAccount.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">vault/GetBankAccount.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="get-bank-account-sample">Get Bank Account Sample</h1>
<p>The Bank Account resource allows you to
retrieve previously saved Bank Accounts.
API called: &#39;/v1/vault/bank-accounts&#39;</p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>The following code takes you through
the process of retrieving a saved Bank Account</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> \PayPal\Api\BankAccount $bankAccount */</span>
<span class="hljs-variable">$bankAccount</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateBankAccount.php'</span>;

<span class="hljs-comment">/// ### Retrieve Bank Account</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$bankAccount</span> = \PayPal\Api\BankAccount::get(<span class="hljs-variable">$bankAccount</span>-&gt;getId(), <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Get Bank Account"</span>, <span class="hljs-string">"Bank Account"</span>, <span class="hljs-variable">$bankAccount</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Get Bank Account"</span>, <span class="hljs-string">"Bank Account"</span>, <span class="hljs-variable">$bankAccount</span>-&gt;getId(), <span class="hljs-keyword">null</span>, <span class="hljs-variable">$bankAccount</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$bankAccount</span>;</div></div></div></div></body></html>
