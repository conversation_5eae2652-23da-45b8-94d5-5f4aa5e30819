<!DOCTYPE html><html lang="en"><head><title>vault/ListCreditCards</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="vault/ListCreditCards"><meta name="groc-project-path" content="vault/ListCreditCards.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">vault/ListCreditCards.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="list-credit-card-sample">List Credit Card Sample</h1>
<p>The CreditCard resource allows you to
retrieve all previously saved CreditCards.
API called: &#39;/v1/vault/credit-cards&#39;
Documentation: <a href="https://developer.paypal.com/webapps/developer/docs/api/#list-credit-card-resources">https://developer.paypal.com/webapps/developer/docs/api/#list-credit-card-resources</a></p></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>Creating a Credit Card just in case</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> CreditCard $card */</span>
<span class="hljs-variable">$card</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateCreditCard.php'</span>;

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">CreditCard</span>;

<span class="hljs-comment">/// ### List All Credit Cards</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="parameters-to-filter">Parameters to Filter</h3>
<p>There are many possible filters that you could apply to it. For complete list, please refere to developer docs at above link.</p></div></div><div class="code"><div class="wrapper">    <span class="hljs-variable">$params</span> = <span class="hljs-keyword">array</span>(
        <span class="hljs-string">"sort_by"</span> =&gt; <span class="hljs-string">"create_time"</span>,
        <span class="hljs-string">"sort_order"</span> =&gt; <span class="hljs-string">"desc"</span>,
        <span class="hljs-string">"merchant_id"</span> =&gt; <span class="hljs-string">"MyStore1"</span>  <span class="hljs-comment">// Filtering by MerchantId set during CreateCreditCard.</span>
    );
    <span class="hljs-variable">$cards</span> = CreditCard::all(<span class="hljs-variable">$params</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"List All Credit Cards"</span>, <span class="hljs-string">"CreditCardList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"List All Credit Cards"</span>, <span class="hljs-string">"CreditCardList"</span>, <span class="hljs-keyword">null</span>, <span class="hljs-variable">$params</span>, <span class="hljs-variable">$cards</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$card</span>;</div></div></div></div></body></html>
