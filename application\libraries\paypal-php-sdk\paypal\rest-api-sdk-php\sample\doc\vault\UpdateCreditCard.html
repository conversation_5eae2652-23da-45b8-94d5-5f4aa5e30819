<!DOCTYPE html><html lang="en"><head><title>vault/UpdateCreditCard</title></head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"><meta name="groc-relative-root" content="../"><meta name="groc-document-path" content="vault/UpdateCreditCard"><meta name="groc-project-path" content="vault/UpdateCreditCard.php"><link rel="stylesheet" type="text/css" media="all" href="../assets/style.css"><script type="text/javascript" src="../assets/behavior.js"></script><body><div id="meta"><div class="file-path">vault/UpdateCreditCard.php</div></div><div id="document"><div class="segment"><div class="code"><div class="wrapper"><span class="hljs-preprocessor">&lt;?php</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h1 id="update-credit-card-sample">Update Credit Card Sample</h1>
<p>The CreditCard resource allows you to
update previously saved CreditCards.
API called: PATCH /v1/vault/credit-cards/<Credit-Card-Id>
The following code takes you through
the process of updating a saved CreditCard</p></div></div><div class="code"><div class="wrapper"><span class="hljs-comment">/** <span class="hljs-doctag">@var</span> CreditCard $card */</span>
<span class="hljs-variable">$card</span> = <span class="hljs-keyword">require</span> <span class="hljs-string">'CreateCreditCard.php'</span>;
<span class="hljs-variable">$id</span> = <span class="hljs-variable">$card</span>-&gt;getId();

<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">CreditCard</span>;
<span class="hljs-keyword">use</span> <span class="hljs-title">PayPal</span>\<span class="hljs-title">Api</span>\<span class="hljs-title">Patch</span>;</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="patch-object">Patch Object</h3>
<p>You could update a credit card by sending patch requests. Each path object would have a specific detail in the object to be updated.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$pathOperation</span> = <span class="hljs-keyword">new</span> Patch();
<span class="hljs-variable">$pathOperation</span>-&gt;setOp(<span class="hljs-string">"replace"</span>)
    -&gt;setPath(<span class="hljs-string">'/expire_month'</span>)
    -&gt;setValue(<span class="hljs-string">"12"</span>);</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><h3 id="another-patch-object">Another Patch Object</h3>
<p>You could set more than one patch while updating a credit card.</p></div></div><div class="code"><div class="wrapper"><span class="hljs-variable">$pathOperation2</span> = <span class="hljs-keyword">new</span> Patch();
<span class="hljs-variable">$pathOperation2</span>-&gt;setOp(<span class="hljs-string">'add'</span>)
    -&gt;setPath(<span class="hljs-string">'/billing_address'</span>)
    -&gt;setValue(json_decode(<span class="hljs-string">'{
            "line1": "111 First Street",
            "city": "Saratoga",
            "country_code": "US",
            "state": "CA",
            "postal_code": "95070"
        }'</span>));

<span class="hljs-variable">$pathRequest</span> = <span class="hljs-keyword">new</span> \PayPal\Api\PatchRequest();
<span class="hljs-variable">$pathRequest</span>-&gt;addPatch(<span class="hljs-variable">$pathOperation</span>)
    -&gt;addPatch(<span class="hljs-variable">$pathOperation2</span>);
<span class="hljs-comment">/// ### Update Credit Card</span></div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>(See bootstrap.php for more on <code>ApiContext</code>)</p></div></div><div class="code"><div class="wrapper"><span class="hljs-keyword">try</span> {
    <span class="hljs-variable">$card</span> = <span class="hljs-variable">$card</span>-&gt;update(<span class="hljs-variable">$pathRequest</span>, <span class="hljs-variable">$apiContext</span>);
} <span class="hljs-keyword">catch</span> (<span class="hljs-keyword">Exception</span> <span class="hljs-variable">$ex</span>) {</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper">    ResultPrinter::printError(<span class="hljs-string">"Updated Credit Card"</span>, <span class="hljs-string">"Credit Card"</span>, <span class="hljs-variable">$card</span>-&gt;getId(), <span class="hljs-variable">$pathRequest</span>, <span class="hljs-variable">$ex</span>);
    <span class="hljs-keyword">exit</span>(<span class="hljs-number">1</span>);
}</div></div></div><div class="segment"><div class="comments "><div class="wrapper"><p>NOTE: PLEASE DO NOT USE RESULTPRINTER CLASS IN YOUR ORIGINAL CODE. FOR SAMPLE ONLY</p></div></div><div class="code"><div class="wrapper"> ResultPrinter::printResult(<span class="hljs-string">"Updated Credit Card"</span>, <span class="hljs-string">"Credit Card"</span>, <span class="hljs-variable">$card</span>-&gt;getId(), <span class="hljs-variable">$pathRequest</span>, <span class="hljs-variable">$card</span>);

<span class="hljs-keyword">return</span> <span class="hljs-variable">$card</span>;</div></div></div></div></body></html>
