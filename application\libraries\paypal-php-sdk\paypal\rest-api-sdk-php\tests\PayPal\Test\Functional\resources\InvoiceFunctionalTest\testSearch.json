{"description": "Search for invoice resources.", "title": "Search for invoice resources.", "runnable": true, "operationId": "invoice.search", "user": {"scopes": []}, "credentials": {"oauth": {"clientId": "", "clientSecret": "", "path": ""}, "login": {}, "openIdConnect": {}}, "request": {"path": "v1/invoicing/search/", "method": "POST", "headers": {"X-PAYPAL-SECURITY-CONTEXT": "{\"actor\":{\"auth_claims\":[\"CLIENT_ID_SECRET\"],\"auth_state\":\"LOGGEDIN\",\"account_number\":\"1942617323817135416\",\"encrypted_account_number\":\"6QNCBKP95EWWN\",\"party_id\":\"1942617323817135416\"},\"auth_token\":\"A015vRRfXmgj2UscSiBbwz1Elw8RW.ypMlPJsMH77snr6fc\",\"auth_token_type\":\"ACCESS_TOKEN\",\"last_validated\":**********,\"scopes\":[\"openid\",\"https://uri.paypal.com/services/invoicing\",\"https://uri.paypal.com/services/subscriptions\",\"https://api.paypal.com/v1/payments/.*\",\"https://api.paypal.com/v1/vault/credit-card/.*\",\"https://api.paypal.com/v1/vault/credit-card\"],\"client_id\":\"AewC1RCK3i4Z7WTbE0cz5buvd_NW17sYbYI4kc29c5qGxeh-0P7sMuXh2chc\",\"claims\":{\"actor_payer_id\":\"6QNCBKP95EWWN\"},\"subjects\":[]}"}, "body": {"page": 0, "page_size": 3, "total_count_required": true}}, "response": {"status": "", "headers": {}, "body": {"total_count": 1, "invoices": [{"id": "INV2-RF6D-L66T-D7H2-CRU7", "number": "0001", "status": "SENT", "merchant_info": {"email": "<EMAIL>"}, "billing_info": [{"email": "<EMAIL>"}], "shipping_info": {"email": "<EMAIL>"}, "invoice_date": "2012-05-09 PST", "payment_term": {"due_date": "2012-05-24 PST"}, "total_amount": {"currency": "USD", "value": "250"}, "metadata": {"created_date": "2012-05-09 04:48:57 PST"}}]}}}