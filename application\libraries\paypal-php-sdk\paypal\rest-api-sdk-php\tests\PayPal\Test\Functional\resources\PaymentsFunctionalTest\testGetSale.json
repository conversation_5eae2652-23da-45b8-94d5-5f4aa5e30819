{"description": "Lookup a Sale resource.", "title": "Sale Resource", "runnable": true, "operationId": "sale.get", "user": {"scopes": []}, "credentials": {"oauth": {"clientId": "", "clientSecret": "", "path": ""}, "login": {}, "openIdConnect": {}}, "request": {"headers": {}, "body": {}, "path": "/v1/sales/4RR959492F879224U", "method": "GET"}, "response": {"headers": {}, "body": {"id": "4RR959492F879224U", "create_time": "2014-10-28T19:27:39Z", "update_time": "2014-10-28T19:28:02Z", "amount": {"total": "7.47", "currency": "USD"}, "payment_mode": "INSTANT_TRANSFER", "state": "completed", "protection_eligibility": "ELIGIBLE", "protection_eligibility_type": "ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE", "parent_payment": "PAY-17S8410768582940NKEE66EQ", "links": [{"href": "https://api.sandbox.paypal.com/v1/payments/sale/5SA006225W236580K", "rel": "self", "method": "GET"}, {"href": "https://api.sandbox.paypal.com/v1/payments/sale/5SA006225W236580K/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.sandbox.paypal.com/v1/payments/payment/PAY-48W25034R6080713AKRH64KY", "rel": "parent_payment", "method": "GET"}]}, "status": "201 OK"}}