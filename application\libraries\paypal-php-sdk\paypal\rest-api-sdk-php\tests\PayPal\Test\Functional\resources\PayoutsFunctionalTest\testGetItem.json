{"description": "Sender needs status of one item.", "title": "GET item sample", "runnable": true, "operationId": "payouts.item", "user": {"scopes": []}, "credentials": {"oauth": {"path": "/v1/oauth/token", "clientId": "", "clientSecret": ""}}, "request": {"path": "v1/payments/payouts-item/452345", "method": "GET", "headers": {"Content-Type": "application/json", "Content-Encoding": "gzip"}, "body": {}}, "response": {"status": "200 OK", "headers": {"Content-Type": "application/json", "Content-Encoding": "gzip"}, "body": {"payout_item_id": "VHBFGN95AWV82", "transaction_status": "PENDING", "payout_item_fee": {"currency": "USD", "value": "0.02"}, "payout_batch_id": "CDZEC5MJ8R5HY", "transaction_id": "0728664497487461D", "sender_batch_id": "2014021887", "payout_item": {"amount": {"currency": "USD", "value": "0.99"}, "note": "Thanks you.", "receiver": "<EMAIL>", "recipient_type": "EMAIL", "sender_item_id": "item_154a716f035001"}, "links": [{"href": "https://api.sandbox.paypal.com/v1/payments/payouts-item/HUUQ5YASYLQFN", "rel": "self", "method": "GET"}, {"href": "https://api.sandbox.paypal.com/v1/payments/payouts/LNLSEVGU4P85S", "rel": "batch", "method": "GET"}]}}}