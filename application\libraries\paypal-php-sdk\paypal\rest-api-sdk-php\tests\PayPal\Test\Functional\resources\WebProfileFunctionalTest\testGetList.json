{"description": "Fetch web experience profile list for a given merchant.", "title": "Get profile list", "runnable": true, "operationId": "web-profile.get-list", "user": {"scopes": ["https://api.paypal.com/v1/payments/.*"]}, "credentials": {"oauth": {"clientId": "test-client-01", "clientSecret": "test_secret_a", "path": "/v1/oauth2/token"}}, "request": {"path": "/v1/payment-experience/web-profiles", "method": "GET", "headers": {"Authorization": "Bearer ECvJ_yBNz_UfMmCvWEbT_2ZWXdzbFFQZ-1Y5K2NGgeHn"}}, "response": {"status": "200", "headers": {"Content-Type": "application/json"}, "body": [{"id": "XP-RFV4-PVD8-AGHJ-8E5J", "name": "someName2", "presentation": {"logo_image": "http://www.ebay.com"}, "input_fields": {"no_shipping": 1, "address_override": 1}, "flow_config": {"landing_page_type": "billing", "bank_txn_pending_url": "http://www.ebay.com"}}, {"id": "XP-A88A-LYLW-8Y3X-E5ER", "name": "someName2", "flow_config": {"landing_page_type": "billing", "bank_txn_pending_url": "http://www.ebay.com"}, "input_fields": {"no_shipping": 1, "address_override": 1}, "presentation": {"logo_image": "http://www.ebay.com"}}, {"id": "XP-RFV4-PVD8-AGHJ-8E5J", "name": "someName2", "flow_config": {"bank_txn_pending_url": "http://www.ebay.com"}, "input_fields": {"no_shipping": 1, "address_override": 1}, "presentation": {"logo_image": "http://www.ebay.com"}}]}}