<?php
/*
  ###########################################################
  # PRODUCT NAME: 	iRestora PLUS - API Report Model
  ###########################################################
  # AUTHER:		Custom Development
  ###########################################################
  # EMAIL:		<EMAIL>
  ###########################################################
  # COPYRIGHTS:		RESERVED BY Door Soft
  ###########################################################
  # WEBSITE:		http://www.doorsoft.co
  ###########################################################
  # This is API Report Model for External System Integration
  ###########################################################
 */
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_Report_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Ambil laporan harian lengkap
     * @param string $date
     * @param int $outlet_id
     * @param int $company_id
     * @return array
     */
    public function getDailySummaryReport($date, $outlet_id, $company_id) {
        // Ambil data detail
        $purchases = $this->_getDailyPurchases($date, $outlet_id, $company_id);
        $sales = $this->_getDailySales($date, $outlet_id, $company_id);
        $expenses = $this->_getDailyExpenses($date, $outlet_id, $company_id);
        $wastes = $this->_getDailyWastes($date, $outlet_id, $company_id);
        $supplier_payments = $this->_getDailySupplierPayments($date, $outlet_id, $company_id);
        $customer_receives = $this->_getDailyCustomerReceives($date, $outlet_id, $company_id);

        // Hitung summary
        $summary = $this->_calculateDailySummary($purchases, $sales, $expenses, $wastes, $supplier_payments, $customer_receives);

        return [
            'summary' => $summary,
            'details' => [
                'purchases' => $purchases,
                'sales' => $sales,
                'expenses' => $expenses,
                'wastes' => $wastes,
                'supplier_payments' => $supplier_payments,
                'customer_receives' => $customer_receives
            ]
        ];
    }

    /**
     * Ambil laporan bulanan lengkap
     * @param int $month
     * @param int $year
     * @param int $outlet_id
     * @param int $company_id
     * @return array
     */
    public function getMonthlySummaryReport($month, $year, $outlet_id, $company_id) {
        // Tentukan range tanggal
        $start_date = sprintf('%04d-%02d-01', $year, $month);
        $end_date = date('Y-m-t', strtotime($start_date));

        // Ambil summary bulanan
        $monthly_summary = $this->_getMonthlySummary($start_date, $end_date, $outlet_id, $company_id);
        
        // Ambil breakdown harian
        $daily_breakdown = $this->_getDailyBreakdown($start_date, $end_date, $outlet_id, $company_id);

        return [
            'summary' => $monthly_summary,
            'daily_breakdown' => $daily_breakdown
        ];
    }

    /**
     * Validasi outlet dan company
     */
    public function validateOutletCompany($outlet_id, $company_id) {
        $this->db->select('id');
        $this->db->from('tbl_outlets');
        $this->db->where('id', $outlet_id);
        $this->db->where('company_id', $company_id);
        $this->db->where('del_status', 'Live');
        return $this->db->get()->num_rows() > 0;
    }

    /**
     * Ambil nama outlet
     */
    public function getOutletName($outlet_id) {
        $this->db->select('outlet_name');
        $this->db->from('tbl_outlets');
        $this->db->where('id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $result = $this->db->get()->row();
        return $result ? $result->outlet_name : '';
    }

    /**
     * Ambil data pembelian harian
     */
    private function _getDailyPurchases($date, $outlet_id, $company_id) {
        $this->db->select('p.*, s.name as supplier_name');
        $this->db->from('tbl_purchase p');
        $this->db->join('tbl_suppliers s', 's.id = p.supplier_id', 'left');
        $this->db->where('p.date', $date);
        $this->db->where('p.outlet_id', $outlet_id);
        $this->db->where('p.del_status', 'Live');
        return $this->db->get()->result();
    }

    /**
     * Ambil data penjualan harian
     */
    private function _getDailySales($date, $outlet_id, $company_id) {
        $this->db->select('s.*, c.name as customer_name, t.name as table_name');
        $this->db->from('tbl_sales s');
        $this->db->join('tbl_customers c', 'c.id = s.customer_id', 'left');
        $this->db->join('tbl_tables t', 't.id = s.table_id', 'left');
        $this->db->where('s.sale_date', $date);
        $this->db->where('s.outlet_id', $outlet_id);
        $this->db->where('s.del_status', 'Live');
        $this->db->where('s.order_status', 3); // Only completed orders
        return $this->db->get()->result();
    }

    /**
     * Ambil data pengeluaran harian
     */
    private function _getDailyExpenses($date, $outlet_id, $company_id) {
        $this->db->select('e.*, ei.name as expense_item_name');
        $this->db->from('tbl_expenses e');
        $this->db->join('tbl_expense_items ei', 'ei.id = e.expense_item_id', 'left');
        $this->db->where('e.date', $date);
        $this->db->where('e.outlet_id', $outlet_id);
        $this->db->where('e.del_status', 'Live');
        return $this->db->get()->result();
    }

    /**
     * Ambil data waste harian
     */
    private function _getDailyWastes($date, $outlet_id, $company_id) {
        $this->db->select('w.*, i.name as ingredient_name');
        $this->db->from('tbl_wastes w');
        $this->db->join('tbl_ingredients i', 'i.id = w.ingredient_id', 'left');
        $this->db->where('w.date', $date);
        $this->db->where('w.outlet_id', $outlet_id);
        $this->db->where('w.del_status', 'Live');
        return $this->db->get()->result();
    }

    /**
     * Ambil data pembayaran supplier harian
     */
    private function _getDailySupplierPayments($date, $outlet_id, $company_id) {
        $this->db->select('sp.*, s.name as supplier_name');
        $this->db->from('tbl_supplier_payments sp');
        $this->db->join('tbl_suppliers s', 's.id = sp.supplier_id', 'left');
        $this->db->where('sp.date', $date);
        $this->db->where('sp.outlet_id', $outlet_id);
        $this->db->where('sp.del_status', 'Live');
        return $this->db->get()->result();
    }

    /**
     * Ambil data penerimaan dari customer harian
     */
    private function _getDailyCustomerReceives($date, $outlet_id, $company_id) {
        $this->db->select('cdr.*, c.name as customer_name');
        $this->db->from('tbl_customer_due_receives cdr');
        $this->db->join('tbl_customers c', 'c.id = cdr.customer_id', 'left');
        $this->db->where('cdr.only_date', $date);
        $this->db->where('cdr.outlet_id', $outlet_id);
        $this->db->where('cdr.del_status', 'Live');
        return $this->db->get()->result();
    }

    /**
     * Hitung summary harian
     */
    private function _calculateDailySummary($purchases, $sales, $expenses, $wastes, $supplier_payments, $customer_receives) {
        // Hitung total pembelian
        $total_purchase = 0;
        $total_purchase_paid = 0;
        $total_purchase_due = 0;
        foreach ($purchases as $purchase) {
            $total_purchase += $purchase->grand_total;
            $total_purchase_paid += $purchase->paid;
            $total_purchase_due += $purchase->due;
        }

        // Hitung total penjualan
        $total_sales = 0;
        $total_sales_paid = 0;
        $total_sales_due = 0;
        $total_discount = 0;
        foreach ($sales as $sale) {
            $total_sales += $sale->total_payable;
            $total_sales_paid += $sale->paid_amount;
            $total_sales_due += $sale->due_amount;
            $total_discount += $sale->disc_actual;
        }

        // Hitung total pengeluaran
        $total_expenses = 0;
        foreach ($expenses as $expense) {
            $total_expenses += $expense->amount;
        }

        // Hitung total waste
        $total_waste_amount = 0;
        foreach ($wastes as $waste) {
            $total_waste_amount += $waste->total_loss;
        }

        // Hitung total pembayaran supplier
        $total_supplier_payments = 0;
        foreach ($supplier_payments as $payment) {
            $total_supplier_payments += $payment->amount;
        }

        // Hitung total penerimaan customer
        $total_customer_receives = 0;
        foreach ($customer_receives as $receive) {
            $total_customer_receives += $receive->amount;
        }

        return [
            'purchases' => [
                'total_amount' => $total_purchase,
                'total_paid' => $total_purchase_paid,
                'total_due' => $total_purchase_due,
                'count' => count($purchases)
            ],
            'sales' => [
                'total_amount' => $total_sales,
                'total_paid' => $total_sales_paid,
                'total_due' => $total_sales_due,
                'total_discount' => $total_discount,
                'count' => count($sales)
            ],
            'expenses' => [
                'total_amount' => $total_expenses,
                'count' => count($expenses)
            ],
            'wastes' => [
                'total_amount' => $total_waste_amount,
                'count' => count($wastes)
            ],
            'supplier_payments' => [
                'total_amount' => $total_supplier_payments,
                'count' => count($supplier_payments)
            ],
            'customer_receives' => [
                'total_amount' => $total_customer_receives,
                'count' => count($customer_receives)
            ],
            'net_cash_flow' => $total_sales_paid + $total_customer_receives - $total_purchase_paid - $total_expenses - $total_supplier_payments
        ];
    }

    /**
     * Ambil summary bulanan
     */
    private function _getMonthlySummary($start_date, $end_date, $outlet_id, $company_id) {
        // Summary pembelian bulanan
        $this->db->select('COUNT(*) as count, SUM(grand_total) as total_amount, SUM(paid) as total_paid, SUM(due) as total_due');
        $this->db->from('tbl_purchase');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $purchase_summary = $this->db->get()->row();

        // Summary penjualan bulanan
        $this->db->select('COUNT(*) as count, SUM(total_payable) as total_amount, SUM(paid_amount) as total_paid, SUM(due_amount) as total_due, SUM(disc_actual) as total_discount');
        $this->db->from('tbl_sales');
        $this->db->where('sale_date >=', $start_date);
        $this->db->where('sale_date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $this->db->where('order_status', 3);
        $sales_summary = $this->db->get()->row();

        // Summary pengeluaran bulanan
        $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
        $this->db->from('tbl_expenses');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $expense_summary = $this->db->get()->row();

        // Summary waste bulanan
        $this->db->select('COUNT(*) as count, SUM(total_loss) as total_amount');
        $this->db->from('tbl_wastes');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $waste_summary = $this->db->get()->row();

        return [
            'purchases' => [
                'total_amount' => $purchase_summary->total_amount ?: 0,
                'total_paid' => $purchase_summary->total_paid ?: 0,
                'total_due' => $purchase_summary->total_due ?: 0,
                'count' => $purchase_summary->count ?: 0
            ],
            'sales' => [
                'total_amount' => $sales_summary->total_amount ?: 0,
                'total_paid' => $sales_summary->total_paid ?: 0,
                'total_due' => $sales_summary->total_due ?: 0,
                'total_discount' => $sales_summary->total_discount ?: 0,
                'count' => $sales_summary->count ?: 0
            ],
            'expenses' => [
                'total_amount' => $expense_summary->total_amount ?: 0,
                'count' => $expense_summary->count ?: 0
            ],
            'wastes' => [
                'total_amount' => $waste_summary->total_amount ?: 0,
                'count' => $waste_summary->count ?: 0
            ],
            'net_cash_flow' => ($sales_summary->total_paid ?: 0) - ($purchase_summary->total_paid ?: 0) - ($expense_summary->total_amount ?: 0)
        ];
    }

    /**
     * Ambil breakdown harian dalam bulan
     */
    private function _getDailyBreakdown($start_date, $end_date, $outlet_id, $company_id) {
        $daily_data = [];

        // Loop setiap hari dalam bulan
        $current_date = $start_date;
        while ($current_date <= $end_date) {
            // Summary penjualan per hari
            $this->db->select('COUNT(*) as count, SUM(total_payable) as total_amount, SUM(paid_amount) as total_paid');
            $this->db->from('tbl_sales');
            $this->db->where('sale_date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $this->db->where('order_status', 3);
            $daily_sales = $this->db->get()->row();

            // Summary pembelian per hari
            $this->db->select('COUNT(*) as count, SUM(grand_total) as total_amount, SUM(paid) as total_paid');
            $this->db->from('tbl_purchase');
            $this->db->where('date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $daily_purchases = $this->db->get()->row();

            // Summary pengeluaran per hari
            $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
            $this->db->from('tbl_expenses');
            $this->db->where('date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $daily_expenses = $this->db->get()->row();

            $daily_data[] = [
                'date' => $current_date,
                'day_name' => date('l', strtotime($current_date)),
                'sales' => [
                    'count' => $daily_sales->count ?: 0,
                    'total_amount' => $daily_sales->total_amount ?: 0,
                    'total_paid' => $daily_sales->total_paid ?: 0
                ],
                'purchases' => [
                    'count' => $daily_purchases->count ?: 0,
                    'total_amount' => $daily_purchases->total_amount ?: 0,
                    'total_paid' => $daily_purchases->total_paid ?: 0
                ],
                'expenses' => [
                    'count' => $daily_expenses->count ?: 0,
                    'total_amount' => $daily_expenses->total_amount ?: 0
                ],
                'net_cash_flow' => ($daily_sales->total_paid ?: 0) - ($daily_purchases->total_paid ?: 0) - ($daily_expenses->total_amount ?: 0)
            ];

            // Next day
            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $daily_data;
    }
}
