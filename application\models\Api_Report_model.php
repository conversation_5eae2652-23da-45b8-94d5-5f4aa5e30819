<?php
/*
  ###########################################################
  # PRODUCT NAME: 	iRestora PLUS - API Report Model
  ###########################################################
  # AUTHER:		Custom Development
  ###########################################################
  # EMAIL:		<EMAIL>
  ###########################################################
  # COPYRIGHTS:		RESERVED BY Door Soft
  ###########################################################
  # WEBSITE:		http://www.doorsoft.co
  ###########################################################
  # This is API Report Model for External System Integration
  ###########################################################
 */
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_Report_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Ambil laporan harian lengkap
     * @param string $date
     * @param int $outlet_id
     * @param int $company_id
     * @return array
     */
    public function getDailySummaryReport($date, $outlet_id, $company_id) {
        // Ambil data detail
        $purchases = $this->_getDailyPurchases($date, $outlet_id, $company_id);
        $sales = $this->_getDailySales($date, $outlet_id, $company_id);
        $expenses = $this->_getDailyExpenses($date, $outlet_id, $company_id);
        $wastes = $this->_getDailyWastes($date, $outlet_id, $company_id);
        $supplier_payments = $this->_getDailySupplierPayments($date, $outlet_id, $company_id);
        $customer_receives = $this->_getDailyCustomerReceives($date, $outlet_id, $company_id);

        // Hitung summary
        $summary = $this->_calculateDailySummary($purchases, $sales, $expenses, $wastes, $supplier_payments, $customer_receives);

        return [
            'summary' => $summary,
            'details' => [
                'purchases' => $purchases,
                'sales' => $sales,
                'expenses' => $expenses,
                'wastes' => $wastes,
                'supplier_payments' => $supplier_payments,
                'customer_receives' => $customer_receives
            ]
        ];
    }

    /**
     * Ambil laporan range tanggal
     * @param string $start_date
     * @param string $end_date
     * @param int $outlet_id
     * @param int $company_id
     * @return array
     */
    public function getDailyRangeReport($start_date, $end_date, $outlet_id, $company_id) {
        // Ambil summary untuk range tanggal
        $range_summary = $this->_getRangeSummary($start_date, $end_date, $outlet_id, $company_id);

        // Ambil breakdown harian untuk range
        $daily_breakdown = $this->_getDailyBreakdown($start_date, $end_date, $outlet_id, $company_id);

        // Ambil detail transaksi untuk range
        $range_details = $this->_getRangeDetails($start_date, $end_date, $outlet_id, $company_id);

        return [
            'summary' => $range_summary,
            'daily_breakdown' => $daily_breakdown,
            'details' => $range_details
        ];
    }

    /**
     * Ambil laporan bulanan lengkap
     * @param int $month
     * @param int $year
     * @param int $outlet_id
     * @param int $company_id
     * @return array
     */
    public function getMonthlySummaryReport($month, $year, $outlet_id, $company_id) {
        // Tentukan range tanggal
        $start_date = sprintf('%04d-%02d-01', $year, $month);
        $end_date = date('Y-m-t', strtotime($start_date));

        // Ambil summary bulanan
        $monthly_summary = $this->_getMonthlySummary($start_date, $end_date, $outlet_id, $company_id);

        // Ambil breakdown harian
        $daily_breakdown = $this->_getDailyBreakdown($start_date, $end_date, $outlet_id, $company_id);

        return [
            'summary' => $monthly_summary,
            'daily_breakdown' => $daily_breakdown
        ];
    }

    /**
     * Validasi outlet dan company
     */
    public function validateOutletCompany($outlet_id, $company_id) {
        $this->db->select('id');
        $this->db->from('tbl_outlets');
        $this->db->where('id', $outlet_id);
        $this->db->where('company_id', $company_id);
        $this->db->where('del_status', 'Live');
        return $this->db->get()->num_rows() > 0;
    }

    /**
     * Ambil nama outlet
     */
    public function getOutletName($outlet_id) {
        $this->db->select('outlet_name');
        $this->db->from('tbl_outlets');
        $this->db->where('id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $result = $this->db->get()->row();
        return $result ? $result->outlet_name : '';
    }

    /**
     * Ambil data pembelian harian
     */
    private function _getDailyPurchases($date, $outlet_id, $company_id) {
        try {
            $this->db->select('p.*, s.name as supplier_name');
            $this->db->from('tbl_purchase p');
            $this->db->join('tbl_suppliers s', 's.id = p.supplier_id', 'left');
            $this->db->where('p.date', $date);
            $this->db->where('p.outlet_id', $outlet_id);
            $this->db->where('p.del_status', 'Live');

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Purchase query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailyPurchases: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data penjualan harian
     */
    private function _getDailySales($date, $outlet_id, $company_id) {
        try {
            $this->db->select('s.*, c.name as customer_name, t.name as table_name');
            $this->db->from('tbl_sales s');
            $this->db->join('tbl_customers c', 'c.id = s.customer_id', 'left');
            $this->db->join('tbl_tables t', 't.id = s.table_id', 'left');
            $this->db->where('s.sale_date', $date);
            $this->db->where('s.outlet_id', $outlet_id);
            $this->db->where('s.del_status', 'Live');
            $this->db->where('s.order_status', 3); // Only completed orders

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Sales query failed: ' . $this->db->last_query());
                return [];
            }

            $result = $query->result();
            log_message('debug', 'Sales query for date ' . $date . ': ' . count($result) . ' records found');
            log_message('debug', 'Sales query: ' . $this->db->last_query());

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailySales: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data pengeluaran harian
     */
    private function _getDailyExpenses($date, $outlet_id, $company_id) {
        try {
            $this->db->select('e.*, ei.name as expense_item_name');
            $this->db->from('tbl_expenses e');
            $this->db->join('tbl_expense_items ei', 'ei.id = e.expense_item_id', 'left');
            $this->db->where('e.date', $date);
            $this->db->where('e.outlet_id', $outlet_id);
            $this->db->where('e.del_status', 'Live');

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Expenses query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailyExpenses: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data waste harian
     */
    private function _getDailyWastes($date, $outlet_id, $company_id) {
        try {
            // Check if table exists first
            if (!$this->db->table_exists('tbl_wastes')) {
                log_message('debug', 'Table tbl_wastes does not exist');
                return [];
            }

            $this->db->select('w.*, i.name as ingredient_name');
            $this->db->from('tbl_wastes w');
            $this->db->join('tbl_ingredients i', 'i.id = w.ingredient_id', 'left');
            $this->db->where('w.date', $date);
            $this->db->where('w.outlet_id', $outlet_id);
            $this->db->where('w.del_status', 'Live');

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Wastes query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailyWastes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data pembayaran supplier harian
     */
    private function _getDailySupplierPayments($date, $outlet_id, $company_id) {
        try {
            // Check if table exists first
            if (!$this->db->table_exists('tbl_supplier_payments')) {
                log_message('debug', 'Table tbl_supplier_payments does not exist');
                return [];
            }

            $this->db->select('sp.*, s.name as supplier_name');
            $this->db->from('tbl_supplier_payments sp');
            $this->db->join('tbl_suppliers s', 's.id = sp.supplier_id', 'left');
            $this->db->where('sp.date', $date);
            $this->db->where('sp.outlet_id', $outlet_id);
            $this->db->where('sp.del_status', 'Live');

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Supplier payments query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailySupplierPayments: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data penerimaan dari customer harian
     */
    private function _getDailyCustomerReceives($date, $outlet_id, $company_id) {
        try {
            // Check if table exists first
            if (!$this->db->table_exists('tbl_customer_due_receives')) {
                log_message('debug', 'Table tbl_customer_due_receives does not exist');
                return [];
            }

            $this->db->select('cdr.*, c.name as customer_name');
            $this->db->from('tbl_customer_due_receives cdr');
            $this->db->join('tbl_customers c', 'c.id = cdr.customer_id', 'left');
            $this->db->where('cdr.only_date', $date);
            $this->db->where('cdr.outlet_id', $outlet_id);
            $this->db->where('cdr.del_status', 'Live');

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Customer receives query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getDailyCustomerReceives: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Hitung summary harian
     */
    private function _calculateDailySummary($purchases, $sales, $expenses, $wastes, $supplier_payments, $customer_receives) {
        // Hitung total pembelian
        $total_purchase = 0;
        $total_purchase_paid = 0;
        $total_purchase_due = 0;
        foreach ($purchases as $purchase) {
            $total_purchase += $purchase->grand_total;
            $total_purchase_paid += $purchase->paid;
            $total_purchase_due += $purchase->due;
        }

        // Hitung total penjualan
        $total_sales = 0;
        $total_sales_paid = 0;
        $total_sales_due = 0;
        $total_discount = 0;
        foreach ($sales as $sale) {
            $total_sales += $sale->total_payable;
            $total_sales_paid += $sale->paid_amount;
            $total_sales_due += $sale->due_amount;
            $total_discount += $sale->disc_actual;
        }

        // Hitung total pengeluaran
        $total_expenses = 0;
        foreach ($expenses as $expense) {
            $total_expenses += $expense->amount;
        }

        // Hitung total waste
        $total_waste_amount = 0;
        foreach ($wastes as $waste) {
            $total_waste_amount += $waste->total_loss;
        }

        // Hitung total pembayaran supplier
        $total_supplier_payments = 0;
        foreach ($supplier_payments as $payment) {
            $total_supplier_payments += $payment->amount;
        }

        // Hitung total penerimaan customer
        $total_customer_receives = 0;
        foreach ($customer_receives as $receive) {
            $total_customer_receives += $receive->amount;
        }

        return [
            'purchases' => [
                'total_amount' => $total_purchase,
                'total_paid' => $total_purchase_paid,
                'total_due' => $total_purchase_due,
                'count' => count($purchases)
            ],
            'sales' => [
                'total_amount' => $total_sales,
                'total_paid' => $total_sales_paid,
                'total_due' => $total_sales_due,
                'total_discount' => $total_discount,
                'count' => count($sales)
            ],
            'expenses' => [
                'total_amount' => $total_expenses,
                'count' => count($expenses)
            ],
            'wastes' => [
                'total_amount' => $total_waste_amount,
                'count' => count($wastes)
            ],
            'supplier_payments' => [
                'total_amount' => $total_supplier_payments,
                'count' => count($supplier_payments)
            ],
            'customer_receives' => [
                'total_amount' => $total_customer_receives,
                'count' => count($customer_receives)
            ],
            'net_cash_flow' => $total_sales_paid + $total_customer_receives - $total_purchase_paid - $total_expenses - $total_supplier_payments
        ];
    }

    /**
     * Ambil summary bulanan
     */
    private function _getMonthlySummary($start_date, $end_date, $outlet_id, $company_id) {
        // Summary pembelian bulanan
        $this->db->select('COUNT(*) as count, SUM(grand_total) as total_amount, SUM(paid) as total_paid, SUM(due) as total_due');
        $this->db->from('tbl_purchase');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $purchase_summary = $this->db->get()->row();

        // Summary penjualan bulanan
        $this->db->select('COUNT(*) as count, SUM(total_payable) as total_amount, SUM(paid_amount) as total_paid, SUM(due_amount) as total_due, SUM(disc_actual) as total_discount');
        $this->db->from('tbl_sales');
        $this->db->where('sale_date >=', $start_date);
        $this->db->where('sale_date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $this->db->where('order_status', 3);
        $sales_summary = $this->db->get()->row();

        // Summary pengeluaran bulanan
        $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
        $this->db->from('tbl_expenses');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $expense_summary = $this->db->get()->row();

        // Summary waste bulanan
        $this->db->select('COUNT(*) as count, SUM(total_loss) as total_amount');
        $this->db->from('tbl_wastes');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $waste_summary = $this->db->get()->row();

        return [
            'purchases' => [
                'total_amount' => $purchase_summary->total_amount ?: 0,
                'total_paid' => $purchase_summary->total_paid ?: 0,
                'total_due' => $purchase_summary->total_due ?: 0,
                'count' => $purchase_summary->count ?: 0
            ],
            'sales' => [
                'total_amount' => $sales_summary->total_amount ?: 0,
                'total_paid' => $sales_summary->total_paid ?: 0,
                'total_due' => $sales_summary->total_due ?: 0,
                'total_discount' => $sales_summary->total_discount ?: 0,
                'count' => $sales_summary->count ?: 0
            ],
            'expenses' => [
                'total_amount' => $expense_summary->total_amount ?: 0,
                'count' => $expense_summary->count ?: 0
            ],
            'wastes' => [
                'total_amount' => $waste_summary->total_amount ?: 0,
                'count' => $waste_summary->count ?: 0
            ],
            'net_cash_flow' => ($sales_summary->total_paid ?: 0) - ($purchase_summary->total_paid ?: 0) - ($expense_summary->total_amount ?: 0)
        ];
    }

    /**
     * Ambil breakdown harian dalam bulan
     */
    private function _getDailyBreakdown($start_date, $end_date, $outlet_id, $company_id) {
        $daily_data = [];

        // Loop setiap hari dalam bulan
        $current_date = $start_date;
        while ($current_date <= $end_date) {
            // Summary penjualan per hari
            $this->db->select('COUNT(*) as count, SUM(total_payable) as total_amount, SUM(paid_amount) as total_paid');
            $this->db->from('tbl_sales');
            $this->db->where('sale_date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $this->db->where('order_status', 3);
            $daily_sales = $this->db->get()->row();

            // Summary pembelian per hari
            $this->db->select('COUNT(*) as count, SUM(grand_total) as total_amount, SUM(paid) as total_paid');
            $this->db->from('tbl_purchase');
            $this->db->where('date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $daily_purchases = $this->db->get()->row();

            // Summary pengeluaran per hari
            $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
            $this->db->from('tbl_expenses');
            $this->db->where('date', $current_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $daily_expenses = $this->db->get()->row();

            $daily_data[] = [
                'date' => $current_date,
                'day_name' => date('l', strtotime($current_date)),
                'sales' => [
                    'count' => $daily_sales->count ?: 0,
                    'total_amount' => $daily_sales->total_amount ?: 0,
                    'total_paid' => $daily_sales->total_paid ?: 0
                ],
                'purchases' => [
                    'count' => $daily_purchases->count ?: 0,
                    'total_amount' => $daily_purchases->total_amount ?: 0,
                    'total_paid' => $daily_purchases->total_paid ?: 0
                ],
                'expenses' => [
                    'count' => $daily_expenses->count ?: 0,
                    'total_amount' => $daily_expenses->total_amount ?: 0
                ],
                'net_cash_flow' => ($daily_sales->total_paid ?: 0) - ($daily_purchases->total_paid ?: 0) - ($daily_expenses->total_amount ?: 0)
            ];

            // Next day
            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $daily_data;
    }

    /**
     * Ambil summary untuk range tanggal
     */
    private function _getRangeSummary($start_date, $end_date, $outlet_id, $company_id) {
        // Summary pembelian untuk range
        $this->db->select('COUNT(*) as count, SUM(grand_total) as total_amount, SUM(paid) as total_paid, SUM(due) as total_due');
        $this->db->from('tbl_purchase');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $purchase_summary = $this->db->get()->row();

        // Summary penjualan untuk range
        $this->db->select('COUNT(*) as count, SUM(total_payable) as total_amount, SUM(paid_amount) as total_paid, SUM(due_amount) as total_due, SUM(disc_actual) as total_discount');
        $this->db->from('tbl_sales');
        $this->db->where('sale_date >=', $start_date);
        $this->db->where('sale_date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $this->db->where('order_status', 3);
        $sales_summary = $this->db->get()->row();

        // Summary pengeluaran untuk range
        $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
        $this->db->from('tbl_expenses');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $expense_summary = $this->db->get()->row();

        // Summary waste untuk range
        $this->db->select('COUNT(*) as count, SUM(total_loss) as total_amount');
        $this->db->from('tbl_wastes');
        $this->db->where('date >=', $start_date);
        $this->db->where('date <=', $end_date);
        $this->db->where('outlet_id', $outlet_id);
        $this->db->where('del_status', 'Live');
        $waste_summary = $this->db->get()->row();

        // Summary supplier payments untuk range
        $supplier_payment_summary = (object)['count' => 0, 'total_amount' => 0];
        if ($this->db->table_exists('tbl_supplier_payments')) {
            $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
            $this->db->from('tbl_supplier_payments');
            $this->db->where('date >=', $start_date);
            $this->db->where('date <=', $end_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $query = $this->db->get();
            if ($query) {
                $supplier_payment_summary = $query->row();
            }
        }

        // Summary customer receives untuk range
        $customer_receive_summary = (object)['count' => 0, 'total_amount' => 0];
        if ($this->db->table_exists('tbl_customer_due_receives')) {
            $this->db->select('COUNT(*) as count, SUM(amount) as total_amount');
            $this->db->from('tbl_customer_due_receives');
            $this->db->where('only_date >=', $start_date);
            $this->db->where('only_date <=', $end_date);
            $this->db->where('outlet_id', $outlet_id);
            $this->db->where('del_status', 'Live');
            $query = $this->db->get();
            if ($query) {
                $customer_receive_summary = $query->row();
            }
        }

        return [
            'date_range' => [
                'start_date' => $start_date,
                'end_date' => $end_date,
                'total_days' => (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) + 1
            ],
            'purchases' => [
                'total_amount' => $purchase_summary->total_amount ?: 0,
                'total_paid' => $purchase_summary->total_paid ?: 0,
                'total_due' => $purchase_summary->total_due ?: 0,
                'count' => $purchase_summary->count ?: 0
            ],
            'sales' => [
                'total_amount' => $sales_summary->total_amount ?: 0,
                'total_paid' => $sales_summary->total_paid ?: 0,
                'total_due' => $sales_summary->total_due ?: 0,
                'total_discount' => $sales_summary->total_discount ?: 0,
                'count' => $sales_summary->count ?: 0
            ],
            'expenses' => [
                'total_amount' => $expense_summary->total_amount ?: 0,
                'count' => $expense_summary->count ?: 0
            ],
            'wastes' => [
                'total_amount' => $waste_summary->total_amount ?: 0,
                'count' => $waste_summary->count ?: 0
            ],
            'supplier_payments' => [
                'total_amount' => $supplier_payment_summary->total_amount ?: 0,
                'count' => $supplier_payment_summary->count ?: 0
            ],
            'customer_receives' => [
                'total_amount' => $customer_receive_summary->total_amount ?: 0,
                'count' => $customer_receive_summary->count ?: 0
            ],
            'net_cash_flow' => ($sales_summary->total_paid ?: 0) + ($customer_receive_summary->total_amount ?: 0) - ($purchase_summary->total_paid ?: 0) - ($expense_summary->total_amount ?: 0) - ($supplier_payment_summary->total_amount ?: 0)
        ];
    }

    /**
     * Ambil detail transaksi untuk range tanggal
     */
    private function _getRangeDetails($start_date, $end_date, $outlet_id, $company_id) {
        // Ambil sample data untuk setiap kategori (limit untuk performance)
        $purchases = $this->_getRangePurchases($start_date, $end_date, $outlet_id, $company_id);
        $sales = $this->_getRangeSales($start_date, $end_date, $outlet_id, $company_id);
        $expenses = $this->_getRangeExpenses($start_date, $end_date, $outlet_id, $company_id);

        return [
            'purchases' => $purchases,
            'sales' => $sales,
            'expenses' => $expenses,
            'note' => 'Detail transaksi dibatasi 100 record per kategori untuk performance'
        ];
    }

    /**
     * Ambil data pembelian untuk range
     */
    private function _getRangePurchases($start_date, $end_date, $outlet_id, $company_id) {
        try {
            $this->db->select('p.*, s.name as supplier_name');
            $this->db->from('tbl_purchase p');
            $this->db->join('tbl_suppliers s', 's.id = p.supplier_id', 'left');
            $this->db->where('p.date >=', $start_date);
            $this->db->where('p.date <=', $end_date);
            $this->db->where('p.outlet_id', $outlet_id);
            $this->db->where('p.del_status', 'Live');
            $this->db->order_by('p.date', 'DESC');
            $this->db->limit(100); // Limit untuk performance

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Range purchases query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getRangePurchases: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data penjualan untuk range
     */
    private function _getRangeSales($start_date, $end_date, $outlet_id, $company_id) {
        try {
            $this->db->select('s.*, c.name as customer_name, t.name as table_name');
            $this->db->from('tbl_sales s');
            $this->db->join('tbl_customers c', 'c.id = s.customer_id', 'left');
            $this->db->join('tbl_tables t', 't.id = s.table_id', 'left');
            $this->db->where('s.sale_date >=', $start_date);
            $this->db->where('s.sale_date <=', $end_date);
            $this->db->where('s.outlet_id', $outlet_id);
            $this->db->where('s.del_status', 'Live');
            $this->db->where('s.order_status', 3);
            $this->db->order_by('s.sale_date', 'DESC');
            $this->db->limit(100); // Limit untuk performance

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Range sales query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getRangeSales: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Ambil data pengeluaran untuk range
     */
    private function _getRangeExpenses($start_date, $end_date, $outlet_id, $company_id) {
        try {
            if (!$this->db->table_exists('tbl_expenses')) {
                return [];
            }

            $this->db->select('e.*, ei.name as expense_item_name');
            $this->db->from('tbl_expenses e');
            $this->db->join('tbl_expense_items ei', 'ei.id = e.expense_item_id', 'left');
            $this->db->where('e.date >=', $start_date);
            $this->db->where('e.date <=', $end_date);
            $this->db->where('e.outlet_id', $outlet_id);
            $this->db->where('e.del_status', 'Live');
            $this->db->order_by('e.date', 'DESC');
            $this->db->limit(100); // Limit untuk performance

            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Range expenses query failed: ' . $this->db->last_query());
                return [];
            }
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Error in _getRangeExpenses: ' . $e->getMessage());
            return [];
        }
    }
}
