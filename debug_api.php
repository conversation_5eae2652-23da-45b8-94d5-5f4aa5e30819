<?php
// Debug API untuk mencari tahu masalah daily report

$base_url = 'http://localhost/irestora_api';
$outlet_id = 1;
$company_id = 1;

echo "=== DEBUG API REPORT ===\n\n";

// Test debug endpoint
echo "1. Testing Debug Endpoint...\n";
$debug_url = $base_url . '/index.php/Api_Report/debug_data';
$debug_data = array(
    'date' => '2025-07-22',
    'outlet_id' => $outlet_id,
    'company_id' => $company_id
);

$debug_response = callAPI('POST', $debug_url, $debug_data);
$debug_result = json_decode($debug_response, true);

if ($debug_result && $debug_result['status']) {
    echo "✓ Debug successful\n";
    echo "Debug Info:\n";
    print_r($debug_result['debug_info']);
    echo "\n";
    
    // <PERSON>i tanggal yang ada data
    if (!empty($debug_result['debug_info']['sales_summary'])) {
        $latest_date = $debug_result['debug_info']['sales_summary'][0]['sale_date'];
        echo "Latest sales date found: " . $latest_date . "\n\n";
        
        // Test simple daily method first
        echo "2. Testing Simple Daily Method with existing date: " . $latest_date . "\n";
        $simple_url = $base_url . '/index.php/Api_Report/simple_daily';
        $simple_data = array(
            'date' => $latest_date,
            'outlet_id' => $outlet_id,
            'company_id' => $company_id
        );

        $simple_response = callAPI('POST', $simple_url, $simple_data);
        echo "Simple Response: " . $simple_response . "\n\n";

        $simple_result = json_decode($simple_response, true);
        if ($simple_result && $simple_result['status']) {
            echo "✓ Simple daily successful\n";
            echo "  Sales Count: " . $simple_result['data']['sales_count'] . "\n";
            echo "  Sales Total: " . number_format($simple_result['data']['sales_total']) . "\n\n";
        }

        // Test dengan tanggal yang ada data
        echo "3. Testing Full Daily Report with existing date: " . $latest_date . "\n";
        $daily_url = $base_url . '/index.php/Api_Report/daily_summary';
        $daily_data = array(
            'date' => $latest_date,
            'outlet_id' => $outlet_id,
            'company_id' => $company_id
        );

        $daily_response = callAPI('POST', $daily_url, $daily_data);
        echo "Daily Response: " . $daily_response . "\n\n";
        
        $daily_result = json_decode($daily_response, true);
        if ($daily_result && $daily_result['status']) {
            echo "✓ Daily report successful with existing date\n";
            echo "  Sales Count: " . $daily_result['data']['summary']['sales']['count'] . "\n";
            echo "  Sales Amount: " . number_format($daily_result['data']['summary']['sales']['total_amount']) . "\n";
        } else {
            echo "✗ Daily report still failed\n";
            if ($daily_result) {
                echo "  Error: " . $daily_result['message'] . "\n";
                if (isset($daily_result['data']['error_details'])) {
                    echo "  Error Details: " . $daily_result['data']['error_details'] . "\n";
                }
            }
        }
    }
} else {
    echo "✗ Debug failed\n";
    echo "Response: " . $debug_response . "\n";
}

function callAPI($method, $url, $data = false) {
    $curl = curl_init();

    switch ($method) {
        case "POST":
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data)
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            break;
        case "PUT":
            curl_setopt($curl, CURLOPT_PUT, 1);
            break;
        default:
            if ($data)
                $url = sprintf("%s?%s", $url, http_build_query($data));
    }

    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));

    $result = curl_exec($curl);
    
    if (curl_error($curl)) {
        echo "cURL Error: " . curl_error($curl) . "\n";
    }
    
    curl_close($curl);
    return $result;
}
?>
