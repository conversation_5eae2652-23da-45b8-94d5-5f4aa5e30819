-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 21, 2025 at 01:54 PM
-- Server version: 5.7.33
-- PHP Version: 7.4.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `iresto`
--

-- --------------------------------------------------------

--
-- Table structure for table `tbl_admin_user_menus`
--

CREATE TABLE `tbl_admin_user_menus` (
  `id` int(11) NOT NULL,
  `label` varchar(100) DEFAULT NULL,
  `function_name` varchar(100) DEFAULT NULL,
  `menu_name` varchar(50) DEFAULT NULL,
  `controller_name` varchar(50) DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `order_by` int(11) DEFAULT NULL,
  `is_ignore` int(11) DEFAULT '0',
  `is_ignore_menu` int(11) DEFAULT '1',
  `parent_id` int(11) DEFAULT '0',
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_admin_user_menus`
--

INSERT INTO `tbl_admin_user_menus` (`id`, `label`, `function_name`, `menu_name`, `controller_name`, `icon`, `order_by`, `is_ignore`, `is_ignore_menu`, `parent_id`, `del_status`) VALUES
(1, 'pos', 'posAndWaiterMiddleman', 'POS', 'POSChecker', NULL, 18, 1, 0, 19, 'Live'),
(2, 'purchase', 'purchases', 'Purchase', 'Purchase', 'truck', 6, 0, 0, 0, 'Live'),
(3, 'inventory', 'index', 'Inventory', 'Inventory', 'server', 9, 0, 0, 0, 'Live'),
(4, 'waste', 'wastes', 'Waste', 'Waste', 'trash-2', 11, 0, 0, 0, 'Live'),
(5, 'expense', 'expenses', 'Expense', 'Expense', 'dollar-sign', 12, 0, 0, 0, 'Live'),
(6, 'report', NULL, 'Report', 'Report', 'file-text', 16, 0, 0, 0, 'Live'),
(7, 'dashboard', 'dashboard', 'Dashboard', 'Dashboard', 'grid', 5, 0, 0, 0, 'Live'),
(8, 'master', NULL, 'Master', 'Master', 'server', 17, 0, 1, 0, 'Live'),
(9, 'user', NULL, 'User', 'User', NULL, 19, 1, 1, 0, 'Live'),
(10, 'supplier_due_payment', 'supplierPayments', 'Supplier Payment', 'SupplierPayment', 'dollar-sign', 13, 0, 0, 0, 'Live'),
(11, 'inventory_adjustments', 'inventoryAdjustments', 'Inventory Adjustment', 'Inventory_adjustment', 'sun', 10, 0, 0, 0, 'Live'),
(12, 'customer_due_receive', 'customerDueReceives', 'Customer Due Receive', 'Customer_due_receive', 'dollar-sign', 14, 0, 0, 0, 'Live'),
(13, 'attendance', 'attendances', 'Attendance', 'Attendance', 'clock', 15, 0, 0, 0, 'Live'),
(14, 'bar', 'panel', 'Bar', 'Bar', NULL, 21, 1, 0, 19, 'Live'),
(15, 'kitchen', 'panel', 'Kitchen', 'Kitchen', NULL, 22, 1, 0, 19, 'Live'),
(16, 'waiter', 'panel', 'Waiter', 'Waiter', NULL, 23, 1, 0, 19, 'Live'),
(17, 'outlets', NULL, 'Outlet', 'Outlet', 'hard-drive', 3, 0, 0, 0, 'Live'),
(18, 'user', NULL, 'User', 'User', NULL, 36, 1, 0, 0, 'Live'),
(19, 'all_screen', NULL, NULL, NULL, 'file-text', 4, 0, 1, 0, 'Live'),
(20, 'account_user', NULL, NULL, NULL, 'settings', 18, 0, 1, 0, 'Live'),
(21, 'home', 'userProfile', 'userProfile', 'Authentication', 'home', 1, 0, 1, 0, 'Live'),
(22, 'setting', 'index', 'index', 'setting', 'settings', 2, 0, 0, 0, 'Live'),
(23, 'sale', 'sales', 'sales', 'Sale', 'shopping-cart', 8, 0, 0, 0, 'Live'),
(24, 'register_report', 'registerReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(25, 'daily_summary_report', 'dailySummaryReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(26, 'food_sale_report', 'foodMenuSales', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(27, 'daily_sale_report', 'saleReportByDate', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(28, 'detailed_sale_report', 'detailedSaleReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(29, 'consumption_report', 'consumptionReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(30, 'inventory_report', 'inventoryReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(31, 'low_inventory_report', 'getInventoryAlertList', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(32, 'profit_loss_report', 'profitLossReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(33, 'kitchen_performance_report', 'kitchenPerformanceReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(34, 'attendance_report', 'attendanceReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(35, 'supplier_ledger', 'supplierReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(36, 'supplier_due_report', 'supplierDueReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(37, 'customer_due_report', 'customerDueReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(38, 'customer_ledger', 'customerReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(39, 'purchase_report', 'purchaseReportByDate', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(40, 'expense_report', 'expenseReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(41, 'waste_report', 'wasteReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(42, 'vat_report', 'vatReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(43, 'ingredient_categories', 'ingredientCategories', NULL, 'ingredientCategory', NULL, NULL, 1, 0, 8, 'Live'),
(44, 'ingredient_units', 'Units', NULL, 'Unit', NULL, NULL, 1, 0, 8, 'Live'),
(45, 'ingredients', 'ingredients', NULL, 'ingredient', NULL, NULL, 1, 0, 8, 'Live'),
(46, 'modifiers', 'modifiers', NULL, 'modifier', NULL, NULL, 1, 0, 8, 'Live'),
(47, 'food_menu_categories', 'foodMenuCategories', NULL, 'foodMenuCategory', NULL, NULL, 1, 0, 8, 'Live'),
(48, 'food_menus', 'foodMenus', NULL, 'foodMenu', NULL, NULL, 1, 0, 8, 'Live'),
(49, 'suppliers', 'suppliers', NULL, 'supplier', NULL, NULL, 1, 0, 8, 'Live'),
(50, 'customers', 'customers', NULL, 'customer', NULL, NULL, 1, 0, 8, 'Live'),
(51, 'expense_items', 'expenseItems', NULL, 'expenseItems', NULL, NULL, 1, 0, 8, 'Live'),
(52, 'payment_methods', 'paymentMethods', NULL, 'paymentMethod', NULL, NULL, 1, 0, 8, 'Live'),
(53, 'tables', 'tables', NULL, 'table', NULL, NULL, 1, 0, 8, 'Live'),
(54, 'manage_users', 'users', NULL, 'User', NULL, NULL, 1, 0, 20, 'Live'),
(55, 'change_profile', 'changeProfile', NULL, 'Authentication', NULL, NULL, 1, 0, 20, 'Live'),
(56, 'change_password', 'changePassword', NULL, 'Authentication', NULL, NULL, 1, 0, 20, 'Live'),
(57, 'logout', 'logOut', NULL, 'Authentication', NULL, NULL, 1, 1, 20, 'Live'),
(59, 'send_sms', 'smsService', 'smsService', 'Short_message_service', 'mail', 15, 0, 0, 0, 'Live'),
(60, 'transfer', 'transfers', 'Transfer', 'Transfer', 'truck', 6, 0, 0, 0, 'Live'),
(62, 'plugin', 'plugins', 'Plugin', 'Plugin', 'circle', 19, 0, 0, 0, 'Live'),
(63, 'Saas', NULL, NULL, NULL, 'file-text', 1, 0, 1, 0, 'Live'),
(70, 'site_setting', 'siteSetting', 'Service', 'Service', NULL, 1, 1, 1, 63, 'Live'),
(71, 'email_setting', 'emailSetting', 'Service', 'Service', NULL, 2, 1, 1, 63, 'Live'),
(72, 'Payment_Setting', 'paymentSetting', 'Service', 'Service', NULL, 3, 1, 1, 63, 'Live'),
(73, 'companies', 'companies', 'Service', 'Service', NULL, 4, 1, 1, 63, 'Live'),
(74, 'payment_history', 'paymentHistory', 'Service', 'Service', NULL, 5, 1, 1, 63, 'Live'),
(75, 'Pricing_Plans', 'pricingPlans', 'Service', 'Service', NULL, 6, 1, 1, 63, 'Live'),
(76, 'foodMenuSaleByCategories', 'foodMenuSaleByCategories', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(77, 'WhiteLabel', 'index', 'White Label', 'WhiteLabel', 'settings', 2, 1, 0, 0, 'Live'),
(78, 'FoodTransferReport', 'foodTransferReport', NULL, 'Report', NULL, NULL, 1, 1, 6, 'Live'),
(79, 'inventory_food_menu', 'inventory_food_menu', 'inventory_food_menu', 'Inventory', 'server', 9, 0, 0, 0, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_attendance`
--

CREATE TABLE `tbl_attendance` (
  `id` int(10) NOT NULL,
  `reference_no` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `employee_id` int(10) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `in_time` time DEFAULT NULL,
  `out_time` time DEFAULT NULL,
  `note` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `tbl_attendance`
--

INSERT INTO `tbl_attendance` (`id`, `reference_no`, `employee_id`, `date`, `in_time`, `out_time`, `note`, `user_id`, `company_id`, `del_status`) VALUES
(1, '000001', 1, '2024-12-03', '10:24:17', '00:00:00', NULL, 1, 1, 'Live'),
(2, '000002', 1, '2024-12-04', '04:24:17', '00:00:00', NULL, 1, 1, 'Live'),
(3, '000003', 1, '2024-12-05', '00:08:01', '00:00:00', NULL, 1, 1, 'Live'),
(4, '000004', 1, '2024-12-07', '03:45:01', '00:00:00', NULL, 1, 1, 'Live'),
(5, '000005', 1, '2024-12-08', '02:51:43', '02:55:37', NULL, 1, 1, 'Live'),
(6, '000006', 1, '2024-12-10', '20:55:32', '00:00:00', NULL, 1, 1, 'Live'),
(7, '000007', 1, '2024-12-12', '23:02:16', '00:00:00', NULL, 1, 1, 'Live'),
(8, '000008', 1, '2024-12-22', '22:08:27', '00:00:00', NULL, 1, 1, 'Live'),
(9, '000009', 24, '2024-12-25', '08:33:37', '00:00:00', NULL, 24, 1, 'Live'),
(10, '000010', 1, '2024-12-25', '10:29:44', '00:00:00', NULL, 1, 1, 'Live'),
(11, '000011', 1, '2024-12-26', '01:05:38', '01:19:21', NULL, 1, 1, 'Live'),
(12, '000012', 1, '2024-12-27', '12:38:20', '00:00:00', NULL, 1, 1, 'Live'),
(13, '000013', 1, '2024-12-28', '17:19:40', '00:00:00', NULL, 1, 1, 'Live'),
(14, '000014', 1, '2024-12-29', '01:47:43', '00:00:00', NULL, 1, 1, 'Live'),
(15, '000015', 1, '2025-01-04', '21:22:14', '00:00:00', NULL, 1, 1, 'Live'),
(16, '000016', 1, '2025-02-17', '00:53:40', '00:00:00', NULL, 1, 1, 'Live'),
(17, '000017', 1, '2025-03-07', '13:54:59', '00:00:00', NULL, 1, 1, 'Live'),
(18, '000018', 1, '2025-03-08', '09:42:33', '00:00:00', NULL, 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_companies`
--

CREATE TABLE `tbl_companies` (
  `id` int(10) NOT NULL,
  `business_name` varchar(50) DEFAULT NULL,
  `website` text,
  `date_format` varchar(50) DEFAULT NULL,
  `zone_name` varchar(50) DEFAULT NULL,
  `currency` varchar(50) DEFAULT NULL,
  `currency_position` varchar(100) DEFAULT NULL,
  `precision` varchar(10) DEFAULT NULL,
  `default_customer` int(11) DEFAULT '1',
  `default_waiter` int(11) DEFAULT NULL,
  `default_payment` int(11) DEFAULT NULL,
  `payment_settings` text,
  `address` varchar(100) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `invoice_footer` varchar(500) DEFAULT NULL,
  `print_format_invoice` varchar(500) DEFAULT '80mm',
  `pre_or_post_payment` varchar(500) DEFAULT 'Post Payment',
  `sms_setting_check` varchar(20) DEFAULT NULL,
  `invoice_logo` text,
  `company_id` int(10) DEFAULT NULL,
  `collect_tax` varchar(50) DEFAULT NULL,
  `tax_title` varchar(100) DEFAULT NULL,
  `tax_registration_no` varchar(100) DEFAULT NULL,
  `tax_is_gst` varchar(50) DEFAULT NULL,
  `state_code` varchar(50) DEFAULT NULL,
  `tax_setting` text,
  `tax_string` varchar(250) DEFAULT NULL,
  `outlet_qty` int(11) DEFAULT NULL,
  `sms_enable_status` int(11) DEFAULT NULL,
  `sms_details` text,
  `custom_label` varchar(200) DEFAULT NULL,
  `custom_url` text,
  `smtp_enable_status` int(11) DEFAULT NULL,
  `smtp_details` text,
  `whatsapp_share_number` varchar(20) DEFAULT NULL,
  `language_manifesto` varchar(20) DEFAULT NULL,
  `white_label` text,
  `company_id_xml` varchar(250) DEFAULT NULL,
  `tax_registration_number` varchar(250) DEFAULT NULL,
  `tax_accounting_basis` varchar(250) DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `plan_id` int(11) DEFAULT NULL,
  `monthly_cost` float DEFAULT NULL,
  `number_of_maximum_users` varchar(10) DEFAULT NULL,
  `number_of_maximum_outlets` varchar(10) DEFAULT NULL,
  `number_of_maximum_invoices` varchar(10) DEFAULT NULL,
  `access_day` varchar(10) DEFAULT NULL,
  `payment_clear` varchar(20) DEFAULT 'No',
  `is_block_all_user` varchar(10) DEFAULT 'No',
  `customer_reviewers` text,
  `counter_details` text,
  `social_link_details` text,
  `email_settings` text,
  `export_daily_sale` varchar(20) DEFAULT NULL,
  `printing_invoice` varchar(30) DEFAULT 'web_browser',
  `receipt_printer_invoice` int(11) DEFAULT NULL,
  `printing_bill` varchar(100) DEFAULT 'web_browser',
  `receipt_printer_bill` varchar(100) DEFAULT NULL,
  `print_format_bill` varchar(100) DEFAULT '80mm',
  `printing_kot` varchar(100) DEFAULT 'web_browser',
  `receipt_printer_kot` varchar(100) DEFAULT NULL,
  `print_format_kot` varchar(100) DEFAULT '80mm',
  `printing_bot` varchar(100) DEFAULT 'web_browser',
  `receipt_printer_bot` varchar(100) DEFAULT NULL,
  `print_format_bot` varchar(100) DEFAULT '80mm',
  `print_server_url_invoice` varchar(100) DEFAULT NULL,
  `print_server_url_bill` varchar(100) DEFAULT NULL,
  `languge_manifesto` varchar(50) DEFAULT NULL,
  `print_server_url_kot` varchar(100) DEFAULT NULL,
  `print_server_url_bot` varchar(100) DEFAULT NULL,
  `service_type` varchar(20) DEFAULT 'delivery',
  `service_amount` varchar(20) DEFAULT NULL,
  `active_code` varchar(20) DEFAULT NULL,
  `is_active` int(11) NOT NULL DEFAULT '1',
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_companies`
--

INSERT INTO `tbl_companies` (`id`, `business_name`, `website`, `date_format`, `zone_name`, `currency`, `currency_position`, `precision`, `default_customer`, `default_waiter`, `default_payment`, `payment_settings`, `address`, `phone`, `invoice_footer`, `print_format_invoice`, `pre_or_post_payment`, `sms_setting_check`, `invoice_logo`, `company_id`, `collect_tax`, `tax_title`, `tax_registration_no`, `tax_is_gst`, `state_code`, `tax_setting`, `tax_string`, `outlet_qty`, `sms_enable_status`, `sms_details`, `custom_label`, `custom_url`, `smtp_enable_status`, `smtp_details`, `whatsapp_share_number`, `language_manifesto`, `white_label`, `company_id_xml`, `tax_registration_number`, `tax_accounting_basis`, `created_date`, `plan_id`, `monthly_cost`, `number_of_maximum_users`, `number_of_maximum_outlets`, `number_of_maximum_invoices`, `access_day`, `payment_clear`, `is_block_all_user`, `customer_reviewers`, `counter_details`, `social_link_details`, `email_settings`, `export_daily_sale`, `printing_invoice`, `receipt_printer_invoice`, `printing_bill`, `receipt_printer_bill`, `print_format_bill`, `printing_kot`, `receipt_printer_kot`, `print_format_kot`, `printing_bot`, `receipt_printer_bot`, `print_format_bot`, `print_server_url_invoice`, `print_server_url_bill`, `languge_manifesto`, `print_server_url_kot`, `print_server_url_bot`, `service_type`, `service_amount`, `active_code`, `is_active`, `del_status`) VALUES
(1, 'Kolam Renang Resto Lembah Cinta', '', 'd/m/Y', 'Asia/Singapore', 'Rp. ', 'Before Amount', '0', 1, 24, 7, '{\"field_2\":\"1\",\"field_3\":\"1\",\"field_5\":\"1\",\"field_2_v\":\"sandbox\",\"field_3_v\":\"demo\",\"field_4_v\":\"1\",\"field_2_key_1\":\"AU4W5_vh3LbxnLpd6w6-Ctk5juhxfHPOSii3R_sFvTnC0HPgf-9T7-TZ2UTjH_2xaP5rBcOHUaKVtPiK\",\"field_2_key_2\":\"EIH1A6UUFtnIKn1OyIejWckUpRDnCQJC7cHNmiDMOOSNr7zPNGvkWq53ULT1Pg3g3eKA89OaQxrd3gi0\",\"field_3_key_1\":\"sk_test_51GqddZFGCHDmFd2QAXjmjrbYpEiVTjx4VrLifrt2BqPgMEDOvaPpE78MJUQjpRitJYiHgsAVUh3MEPbT3S97WsVq00ErmzU133\",\"field_3_key_2\":\"pk_test_51GqddZFGCHDmFd2QQCHEDkicU2Y6AiRvUQySrQVzaarBO9c4VJvq7F8geZWCV3JOQK4ETUJHhDXPDVVN0PXyqfIT00uWJ56gPB\",\"field_4_key_1\":\"rzp_test_pm0zv2KQ07bwi5\",\"field_4_key_2\":\"2LD1rNucYo9aXxfb1cve1oms\",\"paypal_business_email\":\"<EMAIL>\",\"url_paypal\":\"https:\\/\\/www.sandbox.paypal.com\\/cgi-bin\\/webscr\"}', '384, KALIBARY ROAD, PIROJPUR-8500', '***********', 'Thank you for visiting us!', '56mm', 'Post Payment', 'Yes', '0ad0381dbd8a3441dfdabcb3007f1275.png', 1, 'Yes', 'Tittle', '32132', 'No', '0931232', '[{\"id\":\"1\",\"tax\":\"PPN\",\"tax_rate\":\"10\"},{\"id\":1,\"tax\":\"Service\",\"tax_rate\":\"5\"}]', 'PPN:Service:', 2, 5, '{\"email_address\":\"<EMAIL>\",\"password\":\"211\"}', 'Watch Video', 'https://www.facebook.com/', 1, '{\"email_address\":\"<EMAIL>\",\"password\":\"43241\"}', '***********', 'stwtyqxst', '{\"site_name\":\"iRestora PLUS  - Next Gen Restaurant POS\",\r\n\"base_color\":\"#7367f0\",\"system_logo\":\"64eddd725a4dd556617841db3fb97ca1.png\",\"footer\":\"iRestora PLUS - Next Gen Restaurant POS\"}', NULL, NULL, NULL, '2021-03-17 23:42:03', NULL, NULL, NULL, '1', NULL, NULL, 'No', 'No', '[\"{\\\"name\\\":\\\"Colin Smalls\\\",\\\"designation\\\":\\\"Basketball Player\\\",\\\"description\\\":\\\"This cozy restaurant has left the best impressions! Hospitable hosts, delicious dishes, beautiful presentation, wide wine list and wonderful dessert. I recommend to everyone! I would like to come back here again and again.\\\"}\",\"{\\\"name\\\":\\\"Sylvester Stallone\\\",\\\"designation\\\":\\\"Actor\\\",\\\"description\\\":\\\"It\\\\u2019s a great experience. The ambiance is very welcoming and charming. Amazing wines, food and service. Staff are extremely knowledgeable and make great recommendations.\\\"}\",\"{\\\"name\\\":\\\"Billie Eilish\\\",\\\"designation\\\":\\\"Musician\\\",\\\"description\\\":\\\"Excellent food. Menu is extensive and seasonal to a particularly high standard. Definitely fine dining. It can be expensive but worth it and they do different deals on different nights so it\\\\u2019s worth checking them out before you book. Highly recommended.\\\"}\"]', '{\"restaurants\":\"47\",\"users\":\"214\",\"reference\":\"96\",\"daily_transactions\":\"8128\"}', '{\"facebook\":\"https:\\/\\/www.facebook.com\\/\",\"twitter\":\"https:\\/\\/twitter.com\\/\",\"instagram\":\"https:\\/\\/www.instagram.com\\/\",\"youtube\":\"https:\\/\\/www.youtube.com\\/\"}', '{\"enable_status\":\"0\",\"host_name\":\"zakbd.com\",\"port_address\":\"32\",\"email_send_to\":\"\",\"user_name\":\"zakir\",\"password\":\"3skdfj\"}', 'enable', 'web_browser', 0, 'direct_print', '', '80mm', 'web_browser', '', '56mm', 'web_browser', '', '56mm', 'http://***********:81/print_server/', 'http://***********:81/print_server/', '', 'http://***********:81/print_server/', 'http://***********:81/print_server/', 'service', '0', '3332444', 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_customers`
--

CREATE TABLE `tbl_customers` (
  `id` int(10) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(300) DEFAULT NULL,
  `password` varchar(100) DEFAULT NULL,
  `gst_number` varchar(50) DEFAULT NULL,
  `pre_or_post_payment` varchar(20) DEFAULT 'Post Payment',
  `area_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live',
  `date_of_birth` date DEFAULT NULL,
  `date_of_anniversary` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_customers`
--

INSERT INTO `tbl_customers` (`id`, `name`, `phone`, `email`, `address`, `password`, `gst_number`, `pre_or_post_payment`, `area_id`, `user_id`, `company_id`, `del_status`, `date_of_birth`, `date_of_anniversary`) VALUES
(1, 'Walk-in Customer', '', NULL, NULL, NULL, NULL, 'Post Payment', 0, 1, 1, 'Live', NULL, NULL),
(2, 'Sylvester Stallone', '89798', '', 'UK', NULL, '', 'Post Payment', NULL, 1, 1, 'Live', '2021-07-05', '2021-07-05'),
(3, 'Vin Diesel', '1234', '', '', NULL, '', 'Post Payment', NULL, 1, 1, 'Deleted', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_customer_due_receives`
--

CREATE TABLE `tbl_customer_due_receives` (
  `id` int(10) NOT NULL,
  `reference_no` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_id` int(11) DEFAULT NULL,
  `only_date` date DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `amount` float DEFAULT NULL,
  `note` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_expenses`
--

CREATE TABLE `tbl_expenses` (
  `id` int(10) NOT NULL,
  `date` date DEFAULT NULL,
  `amount` float DEFAULT NULL,
  `category_id` int(10) DEFAULT NULL,
  `employee_id` int(10) DEFAULT NULL,
  `note` varchar(200) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `outlet_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_expense_items`
--

CREATE TABLE `tbl_expense_items` (
  `id` int(10) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_food_menus`
--

CREATE TABLE `tbl_food_menus` (
  `id` int(10) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `category_id` int(10) DEFAULT NULL,
  `description` varchar(200) DEFAULT NULL,
  `sale_price` float DEFAULT NULL,
  `tax_information` text,
  `tax_string` varchar(250) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `photo` varchar(250) DEFAULT NULL,
  `veg_item` varchar(50) DEFAULT 'Veg No',
  `beverage_item` varchar(50) DEFAULT 'Beverage No',
  `bar_item` varchar(50) DEFAULT 'Bar No',
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_food_menus`
--

INSERT INTO `tbl_food_menus` (`id`, `code`, `name`, `category_id`, `description`, `sale_price`, `tax_information`, `tax_string`, `user_id`, `company_id`, `photo`, `veg_item`, `beverage_item`, `bar_item`, `del_status`) VALUES
(1, '01', 'Sop Ikan Laut &amp; Ikan Goreng Laut', 1, '', 35000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(2, '02', 'Crispy Cumi With Tar Tar Sauce ', 2, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(3, '03', 'Kentang Goreng', 2, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(4, '04', 'Sosis Goreng', 2, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(5, '05', 'Spring Roll Basah', 2, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(6, '06', 'Nasi Goreng Biasa ', 3, '', 20000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(7, '07', 'Nasi Goreng Special', 3, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(8, '08', 'Mie Goreng Tek Sanghai ', 3, '', 20000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(9, '09', 'Indomie Goreng ', 3, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(10, '10', 'Indomie Kuah', 3, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(11, '11', 'Sapi Lada Hitam ', 3, '', 30000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(12, '12', 'Ayam Lalapan', 3, '', 20000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(13, '13', 'Cumi Asam Manis ', 3, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(14, '14', 'Udang Asam Manis ', 3, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(15, '15', 'Bakso Ayam', 3, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(16, '16', 'Bakso Sapi ', 3, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(17, '17', 'Tumis Kangkung', 3, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(18, '18', 'Spaghetti Carbonara ', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(19, '19', 'Spaghetti Bolognese', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(20, '20', 'Fish &amp; Cheap ', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(21, '21', 'Grill Tuna Garlic Butter Sauce ', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(22, '022', 'Chicken Steak Mushroom Sauce', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(23, '23', 'Chicken Gordon Blue Blackpaper SC', 4, '', 35000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(24, '24', 'Crispy Fried Chicken  Steam veg', 4, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(25, '25', 'Mongolian Beef', 4, '', 30000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(26, '26', 'Pisang Goreng', 5, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(27, '27', 'Ice Cream Sundae', 5, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(28, '28', 'Es Teh', 9, '', 5000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(29, '29', 'Es Jeruk ', 9, '', 7000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(30, '30', 'Soda Gembira ', 9, '', 12000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(31, '31', 'Lemon Tea ', 9, '', 8000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(32, '32', 'Minerall Water', 9, '', 5000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(33, '33', 'Es Buah ', 9, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(34, '34', 'Jus Naga', 9, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(35, '35', 'Jus Mangga ', 9, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(36, '36', 'Jus Alpukat ', 9, '', 12000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(37, '37', 'Jus Semangka', 9, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(38, '38', 'Jus Nanas ', 9, '', 12000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(39, '39', 'Beer Bintang Kaleng ', 9, '', 25000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(40, '40', 'Beer Bintang Besar ', 9, '', 40000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(41, '41', 'Beer Bintang Kecil', 9, '', 30000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(42, '42', 'Beer Heineken ', 9, '', 30000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(43, '43', 'Espresso ', 6, '', 11000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(44, '44', 'Cappucino ', 6, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(45, '45', 'Mochacino', 6, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(46, '46', 'Americano ', 6, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(47, '47', 'Long Black', 6, '', 15000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(48, '48', 'Coffee Bali ', 6, '', 5000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(49, '49', 'Iced Latte ', 6, '', 17000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(50, '50', 'Iced Cappucino ', 6, '', 17000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(51, '51', 'Iced Mochacino ', 6, '', 17000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(52, '52', 'Iced Long Black ', 6, '', 17000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(53, '53', 'Iced Americano', 6, '', 17000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(54, '54', 'Iced / Hot Chocolate ', 7, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(55, '55', 'Iced / Hot Red Velvet ', 7, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(56, '56', 'Iced / Hot Matcha ', 7, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(57, '57', 'Iced / Hot Taro ', 7, '', 10000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(58, '58', 'Lemonade ', 7, '', 8000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Live'),
(59, '59', 'Iced Lyche Tea ', 7, '', 8000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Bev No', 'Bar No', 'Live'),
(60, '060', 'tes', 9, '', 15000000, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\"}]', 'PPN:Service:', 1, 1, NULL, 'Veg No', 'Beverage No', 'Bar No', 'Deleted');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_food_menus_ingredients`
--

CREATE TABLE `tbl_food_menus_ingredients` (
  `id` bigint(50) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `consumption` float DEFAULT NULL,
  `food_menu_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_food_menus_modifiers`
--

CREATE TABLE `tbl_food_menus_modifiers` (
  `id` bigint(50) NOT NULL,
  `modifier_id` int(10) DEFAULT NULL,
  `food_menu_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_food_menu_categories`
--

CREATE TABLE `tbl_food_menu_categories` (
  `id` int(10) NOT NULL,
  `category_name` varchar(50) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(10) UNSIGNED DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_food_menu_categories`
--

INSERT INTO `tbl_food_menu_categories` (`id`, `category_name`, `description`, `user_id`, `company_id`, `del_status`) VALUES
(1, 'Menu Spesial', '', 1, 1, 'Live'),
(2, 'Appetizers', '', 1, 1, 'Live'),
(3, 'Indonesian food', '', 1, 1, 'Live'),
(4, 'Mix food', '', 1, 1, 'Live'),
(5, 'Dessert', '', 1, 1, 'Live'),
(6, 'Coffee', '', 1, 1, 'Live'),
(7, 'Non Coffee', '', 1, 1, 'Live'),
(8, 'Teas', '', 1, 1, 'Deleted'),
(9, 'Minuman', '', 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_holds`
--

CREATE TABLE `tbl_holds` (
  `id` int(10) NOT NULL,
  `customer_id` varchar(50) DEFAULT NULL,
  `hold_no` varchar(30) NOT NULL DEFAULT '000000',
  `total_items` int(10) DEFAULT NULL,
  `sub_total` float DEFAULT NULL,
  `paid_amount` double DEFAULT NULL,
  `due_amount` double DEFAULT NULL,
  `due_payment_date` date DEFAULT NULL,
  `disc` varchar(50) DEFAULT NULL,
  `disc_actual` float DEFAULT NULL,
  `vat` float DEFAULT NULL,
  `total_payable` float DEFAULT NULL,
  `payment_method_id` int(10) DEFAULT NULL,
  `table_id` int(10) DEFAULT NULL,
  `total_item_discount_amount` float NOT NULL,
  `sub_total_with_discount` float NOT NULL,
  `sub_total_discount_amount` float NOT NULL,
  `total_discount_amount` float NOT NULL,
  `charge_type` varchar(30) DEFAULT NULL,
  `delivery_charge` float NOT NULL,
  `sub_total_discount_value` varchar(10) NOT NULL,
  `sub_total_discount_type` varchar(20) NOT NULL,
  `token_no` varchar(50) DEFAULT NULL,
  `sale_date` varchar(20) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sale_time` varchar(15) NOT NULL,
  `user_id` int(10) DEFAULT NULL,
  `waiter_id` int(10) DEFAULT '0',
  `outlet_id` int(10) DEFAULT NULL,
  `order_status` tinyint(1) DEFAULT NULL COMMENT '1=new order, 2=cancelled order, 3=invoiced order',
  `sale_vat_objects` text,
  `order_type` tinyint(1) DEFAULT NULL COMMENT '1=dine in, 2 = take away, 3 = delivery',
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_holds_details`
--

CREATE TABLE `tbl_holds_details` (
  `id` int(10) NOT NULL,
  `food_menu_id` int(10) DEFAULT NULL,
  `menu_name` varchar(50) DEFAULT NULL,
  `qty` int(10) DEFAULT NULL,
  `menu_price_without_discount` float NOT NULL,
  `menu_price_with_discount` float NOT NULL,
  `menu_unit_price` float NOT NULL,
  `menu_vat_percentage` float NOT NULL,
  `menu_taxes` text,
  `menu_discount_value` varchar(20) DEFAULT NULL,
  `discount_type` varchar(20) NOT NULL,
  `menu_note` varchar(150) DEFAULT NULL,
  `discount_amount` double DEFAULT NULL,
  `holds_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_holds_details_modifiers`
--

CREATE TABLE `tbl_holds_details_modifiers` (
  `id` int(15) NOT NULL,
  `modifier_id` int(15) NOT NULL,
  `modifier_price` float NOT NULL,
  `food_menu_id` int(10) NOT NULL,
  `holds_id` int(15) NOT NULL,
  `holds_details_id` int(15) NOT NULL,
  `menu_vat_percentage` float DEFAULT NULL,
  `menu_taxes` text,
  `user_id` int(15) NOT NULL,
  `outlet_id` int(15) NOT NULL,
  `customer_id` int(15) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_holds_table`
--

CREATE TABLE `tbl_holds_table` (
  `id` bigint(50) NOT NULL,
  `persons` int(5) NOT NULL,
  `booking_time` datetime NOT NULL,
  `hold_id` int(10) NOT NULL,
  `hold_no` varchar(20) NOT NULL,
  `outlet_id` int(10) NOT NULL,
  `table_id` int(10) NOT NULL,
  `del_status` varchar(20) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='tbl_hold_table';

-- --------------------------------------------------------

--
-- Table structure for table `tbl_ingredients`
--

CREATE TABLE `tbl_ingredients` (
  `id` int(10) NOT NULL,
  `code` varchar(50) NOT NULL DEFAULT '0',
  `name` varchar(50) DEFAULT NULL,
  `category_id` int(10) DEFAULT NULL,
  `purchase_price` float DEFAULT NULL,
  `alert_quantity` float DEFAULT NULL,
  `unit_id` int(10) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_ingredient_categories`
--

CREATE TABLE `tbl_ingredient_categories` (
  `id` int(10) NOT NULL,
  `category_name` varchar(50) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_inventory_adjustment`
--

CREATE TABLE `tbl_inventory_adjustment` (
  `id` int(11) NOT NULL,
  `reference_no` varchar(50) DEFAULT NULL,
  `date` date NOT NULL,
  `note` varchar(200) DEFAULT NULL,
  `employee_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_inventory_adjustment_ingredients`
--

CREATE TABLE `tbl_inventory_adjustment_ingredients` (
  `id` int(11) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `consumption_amount` float DEFAULT NULL,
  `inventory_adjustment_id` int(10) DEFAULT NULL,
  `consumption_status` enum('Plus','Minus') DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_menu_list`
--

CREATE TABLE `tbl_menu_list` (
  `id` int(11) NOT NULL,
  `label` varchar(100) DEFAULT NULL,
  `controller` varchar(100) DEFAULT NULL,
  `initial_function` varchar(100) DEFAULT NULL,
  `order_by` int(11) DEFAULT NULL,
  `del_status` varchar(20) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_modifiers`
--

CREATE TABLE `tbl_modifiers` (
  `id` int(11) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `price` float DEFAULT NULL,
  `description` varchar(300) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `tax_information` text,
  `tax_string` varchar(250) DEFAULT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf32;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_modifier_ingredients`
--

CREATE TABLE `tbl_modifier_ingredients` (
  `id` bigint(50) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `consumption` float DEFAULT NULL,
  `modifier_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_notifications`
--

CREATE TABLE `tbl_notifications` (
  `id` bigint(50) NOT NULL,
  `notification` text NOT NULL,
  `sale_id` int(15) NOT NULL,
  `waiter_id` int(11) DEFAULT NULL,
  `push_status` int(11) NOT NULL DEFAULT '1',
  `outlet_id` int(15) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_notifications`
--

INSERT INTO `tbl_notifications` (`id`, `notification`, `sale_id`, `waiter_id`, `push_status`, `outlet_id`) VALUES
(1, 'Customer: Walk-in Customer, Order Number: B 000033 Take Away order is ready to take', 33, 24, 1, 1);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_notification_bar_kitchen_panel`
--

CREATE TABLE `tbl_notification_bar_kitchen_panel` (
  `id` int(15) NOT NULL,
  `notification` text NOT NULL,
  `sale_id` int(15) NOT NULL,
  `outlet_id` int(15) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_notification_bar_kitchen_panel`
--

INSERT INTO `tbl_notification_bar_kitchen_panel` (`id`, `notification`, `sale_id`, `outlet_id`) VALUES
(1, 'Order:B 000026 has been modified', 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_orders_table`
--

CREATE TABLE `tbl_orders_table` (
  `id` bigint(50) NOT NULL,
  `persons` int(5) NOT NULL,
  `booking_time` datetime NOT NULL,
  `sale_id` int(10) NOT NULL,
  `sale_no` varchar(20) NOT NULL,
  `outlet_id` int(10) NOT NULL,
  `table_id` int(10) NOT NULL,
  `del_status` varchar(20) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='tbl_hold_table';

--
-- Dumping data for table `tbl_orders_table`
--

INSERT INTO `tbl_orders_table` (`id`, `persons`, `booking_time`, `sale_id`, `sale_no`, `outlet_id`, `table_id`, `del_status`) VALUES
(7, 1, '2024-12-25 17:24:02', 9, '000009', 1, 1, 'Deleted'),
(11, 1, '2024-12-25 17:28:41', 13, '000013', 1, 1, 'Deleted'),
(12, 1, '2024-12-25 17:51:46', 14, '000014', 1, 1, 'Deleted'),
(13, 1, '2024-12-25 18:57:30', 15, '000015', 1, 1, 'Deleted');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_outlets`
--

CREATE TABLE `tbl_outlets` (
  `id` int(10) NOT NULL,
  `outlet_name` varchar(50) DEFAULT NULL,
  `outlet_code` varchar(10) DEFAULT NULL,
  `address` varchar(100) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `default_waiter` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `food_menus` text,
  `food_menu_prices` text,
  `has_kitchen` varchar(10) NOT NULL DEFAULT 'No',
  `active_status` varchar(20) DEFAULT 'active',
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_outlets`
--

INSERT INTO `tbl_outlets` (`id`, `outlet_name`, `outlet_code`, `address`, `phone`, `email`, `default_waiter`, `company_id`, `food_menus`, `food_menu_prices`, `has_kitchen`, `active_status`, `del_status`) VALUES
(1, 'Kolam Renang &amp; Resto Lembah Cinta', '000001', 'Jl. Gn. Lempuyang, No 99X', '087762010373', '<EMAIL>', 0, 1, '32,42,25,19,43,33,31,40,37,18,55,39,24,23,21,22,29,20,27,26,34,28,41,36,54,53,35,30,38', '{\"tmp32\":\"111\",\"tmp42\":\"400\",\"tmp25\":\"330\",\"tmp19\":\"222\",\"tmp43\":\"300\",\"tmp33\":\"500\",\"tmp31\":\"333\",\"tmp40\":\"400\",\"tmp37\":\"250\",\"tmp18\":\"444\",\"tmp55\":\"300\",\"tmp39\":\"300\",\"tmp24\":\"400\",\"tmp23\":\"300\",\"tmp21\":\"300\",\"tmp22\":\"300\",\"tmp29\":\"200\",\"tmp20\":\"300\",\"tmp27\":\"200\",\"tmp26\":\"150\",\"tmp34\":\"150\",\"tmp28\":\"250\",\"tmp41\":\"400\",\"tmp36\":\"450\",\"tmp54\":\"70\",\"tmp53\":\"33\",\"tmp35\":\"300\",\"tmp30\":\"200\"}', '', 'active', 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_payment_histories`
--

CREATE TABLE `tbl_payment_histories` (
  `id` int(11) NOT NULL,
  `payment_type` varchar(20) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `payment_date` varchar(20) DEFAULT NULL,
  `amount` float DEFAULT NULL,
  `trans_id` varchar(100) DEFAULT NULL,
  `json_details` text,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_payment_methods`
--

CREATE TABLE `tbl_payment_methods` (
  `id` int(10) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(50) NOT NULL,
  `user_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `tbl_payment_methods`
--

INSERT INTO `tbl_payment_methods` (`id`, `name`, `description`, `user_id`, `company_id`, `del_status`) VALUES
(4, 'Card', '', 1, 1, 'Live'),
(5, 'Paypal', '', 1, 1, 'Live'),
(7, 'Cash', '', 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_plugins`
--

CREATE TABLE `tbl_plugins` (
  `id` int(11) NOT NULL,
  `name` varchar(250) NOT NULL,
  `product_id` int(11) NOT NULL,
  `details` varchar(250) NOT NULL,
  `bestoro` varchar(100) NOT NULL,
  `active_status` varchar(10) NOT NULL DEFAULT 'Active',
  `installation_date` varchar(20) DEFAULT NULL,
  `version` varchar(20) NOT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `tbl_plugins`
--

INSERT INTO `tbl_plugins` (`id`, `name`, `product_id`, `details`, `bestoro`, `active_status`, `installation_date`, `version`, `company_id`, `del_status`) VALUES
(1, 'Saas', 23033741, 'iRestora PLUS - Next Gen Restaurant POS Saas Module', 'fTzfWnSWR', 'Active', '2021-03-22', '1.1', 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_pricing_plans`
--

CREATE TABLE `tbl_pricing_plans` (
  `id` int(11) NOT NULL,
  `plan_name` varchar(100) DEFAULT NULL,
  `payment_type` text,
  `link_for_paypal` text,
  `link_for_stripe` text,
  `monthly_cost` float DEFAULT NULL,
  `number_of_maximum_users` varchar(100) DEFAULT NULL,
  `number_of_maximum_outlets` varchar(100) DEFAULT NULL,
  `number_of_maximum_invoices` varchar(100) DEFAULT NULL,
  `trail_days` varchar(100) DEFAULT NULL,
  `is_recommended` varchar(10) DEFAULT 'No',
  `description` varchar(250) DEFAULT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `tbl_pricing_plans`
--

INSERT INTO `tbl_pricing_plans` (`id`, `plan_name`, `payment_type`, `link_for_paypal`, `link_for_stripe`, `monthly_cost`, `number_of_maximum_users`, `number_of_maximum_outlets`, `number_of_maximum_invoices`, `trail_days`, `is_recommended`, `description`, `del_status`) VALUES
(1, 'Silver', '2', '', '', 10.99, '2', '2', '500', '15', 'No', '', 'Live'),
(2, 'Gold', '2', '', '', 20.99, '5', '5', '2000', '15', 'No', '', 'Live'),
(3, 'Platinum', '2', '', '', 29.99, '10', '7', '5000', '15', 'Yes', '', 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_printers`
--

CREATE TABLE `tbl_printers` (
  `id` int(11) NOT NULL,
  `path` varchar(300) DEFAULT NULL,
  `title` varchar(250) DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL,
  `profile_` varchar(100) DEFAULT NULL,
  `characters_per_line` int(11) DEFAULT NULL,
  `printer_ip_address` varchar(20) DEFAULT NULL,
  `printer_port` varchar(20) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_purchase`
--

CREATE TABLE `tbl_purchase` (
  `id` int(10) NOT NULL,
  `reference_no` varchar(50) DEFAULT NULL,
  `supplier_id` int(10) DEFAULT NULL,
  `date` varchar(15) NOT NULL,
  `subtotal` float DEFAULT NULL,
  `other` float DEFAULT NULL,
  `grand_total` float DEFAULT NULL,
  `paid` float DEFAULT NULL,
  `due` float DEFAULT NULL,
  `note` varchar(200) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_purchase_ingredients`
--

CREATE TABLE `tbl_purchase_ingredients` (
  `id` int(10) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `unit_price` float DEFAULT NULL,
  `quantity_amount` float DEFAULT NULL,
  `total` float DEFAULT NULL,
  `purchase_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_register`
--

CREATE TABLE `tbl_register` (
  `id` int(15) NOT NULL,
  `opening_balance` float DEFAULT NULL,
  `closing_balance` float DEFAULT NULL,
  `opening_balance_date_time` datetime DEFAULT NULL,
  `closing_balance_date_time` datetime DEFAULT NULL,
  `sale_paid_amount` float DEFAULT NULL,
  `customer_due_receive` float DEFAULT NULL,
  `payment_methods_sale` text,
  `register_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1=open,2=closed',
  `user_id` int(15) DEFAULT NULL,
  `outlet_id` int(15) DEFAULT NULL,
  `company_id` int(15) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_register`
--

INSERT INTO `tbl_register` (`id`, `opening_balance`, `closing_balance`, `opening_balance_date_time`, `closing_balance_date_time`, `sale_paid_amount`, `customer_due_receive`, `payment_methods_sale`, `register_status`, `user_id`, `outlet_id`, `company_id`) VALUES
(1, 1, 0, '2024-12-23 12:11:31', NULL, NULL, NULL, NULL, 1, 1, 1, 1),
(2, 1, 0, '2024-12-25 08:36:07', NULL, NULL, NULL, NULL, 1, 24, 1, 1);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sales`
--

CREATE TABLE `tbl_sales` (
  `id` int(10) NOT NULL,
  `customer_id` varchar(50) DEFAULT NULL,
  `sale_no` varchar(30) NOT NULL DEFAULT '000000',
  `total_items` int(10) DEFAULT NULL,
  `sub_total` float DEFAULT NULL,
  `paid_amount` double DEFAULT NULL,
  `due_amount` float DEFAULT NULL,
  `disc` varchar(50) DEFAULT NULL,
  `disc_actual` float DEFAULT NULL,
  `vat` float DEFAULT NULL,
  `total_payable` float DEFAULT NULL,
  `payment_method_id` int(10) DEFAULT NULL,
  `close_time` time NOT NULL,
  `table_id` int(10) DEFAULT NULL,
  `total_item_discount_amount` float NOT NULL,
  `sub_total_with_discount` float NOT NULL,
  `sub_total_discount_amount` float NOT NULL,
  `total_discount_amount` float NOT NULL,
  `charge_type` varchar(30) DEFAULT NULL,
  `delivery_charge` varchar(100) DEFAULT NULL,
  `delivery_charge_actual_charge` float DEFAULT NULL,
  `sub_total_discount_value` varchar(10) NOT NULL,
  `sub_total_discount_type` varchar(20) NOT NULL,
  `sale_date` varchar(20) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `order_time` time NOT NULL,
  `cooking_start_time` datetime NOT NULL,
  `cooking_done_time` datetime NOT NULL,
  `modified` enum('Yes','No') NOT NULL DEFAULT 'No',
  `user_id` int(10) DEFAULT NULL,
  `waiter_id` int(10) NOT NULL DEFAULT '0',
  `outlet_id` int(10) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order, 2=invoiced order, 3=closed order',
  `order_type` tinyint(1) NOT NULL COMMENT '1=dine in, 2 = take away, 3 = delivery',
  `del_status` varchar(50) DEFAULT 'Live',
  `given_amount` float DEFAULT NULL,
  `change_amount` float DEFAULT NULL,
  `sale_vat_objects` text,
  `future_sale_status` int(11) NOT NULL DEFAULT '1',
  `is_kitchen_bell` int(11) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_sales`
--

INSERT INTO `tbl_sales` (`id`, `customer_id`, `sale_no`, `total_items`, `sub_total`, `paid_amount`, `due_amount`, `disc`, `disc_actual`, `vat`, `total_payable`, `payment_method_id`, `close_time`, `table_id`, `total_item_discount_amount`, `sub_total_with_discount`, `sub_total_discount_amount`, `total_discount_amount`, `charge_type`, `delivery_charge`, `delivery_charge_actual_charge`, `sub_total_discount_value`, `sub_total_discount_type`, `sale_date`, `date_time`, `order_time`, `cooking_start_time`, `cooking_done_time`, `modified`, `user_id`, `waiter_id`, `outlet_id`, `company_id`, `order_status`, `order_type`, `del_status`, `given_amount`, `change_amount`, `sale_vat_objects`, `future_sale_status`, `is_kitchen_bell`) VALUES
(9, '1', '000009', 1, 35, 45.5, 0, NULL, NULL, 5.25, 45.5, 4, '17:24:33', NULL, 0, 35, 0, 0, 'service', '15%', 5.25, '', 'fixed', '2024-12-25', '2024-12-25 09:24:02', '17:24:02', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 45.5, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(13, '1', '000013', 1, 35, 42, 0, NULL, NULL, 5.25, 42, 7, '17:29:08', NULL, 0, 35, 0, 0, 'service', '5%', 1.75, '', 'fixed', '2024-12-25', '2024-12-25 09:28:41', '17:28:41', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 42, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(14, '1', '000014', 1, 35, 40.25, 0, NULL, NULL, 5.25, 40.25, 7, '17:52:01', NULL, 0, 35, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-25', '2024-12-25 09:51:46', '17:51:46', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 40.25, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(15, '1', '000015', 1, 35, 45.25, 0, NULL, NULL, 5.25, 45.25, 7, '18:58:41', NULL, 0, 35, 0, 0, 'service', '5', 5, '', 'fixed', '2024-12-25', '2024-12-25 10:57:30', '18:57:30', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 450, 404.75, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(16, '1', '000016', 2, 45, 51.75, 0, NULL, NULL, 6.75, 51.75, 7, '19:15:35', NULL, 0, 45, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-25', '2024-12-25 11:14:59', '19:14:59', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 51.75, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"6.750\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(17, '1', '000017', 3, 45, 51.75, 0, NULL, NULL, 6.75, 51.75, 7, '19:34:05', NULL, 0, 45, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-25', '2024-12-25 11:28:04', '19:28:04', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 51.75, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"VAT\",\"tax_field_amount\":\"6.750\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"SD\",\"tax_field_amount\":\"0.000\"}]', 1, 1),
(18, '1', '000018', 3, 70, 80.5, 0, NULL, NULL, 10.5, 80.5, 7, '01:13:36', NULL, 0, 70, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-25', '2024-12-25 12:10:04', '20:10:04', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 80.5, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"7.000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3.500\"}]', 1, 1),
(19, '1', '000019', 2, 45000, 51750, 0, NULL, NULL, 6750, 51750, 7, '01:25:46', NULL, 0, 45000, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-25', '2024-12-25 17:14:19', '01:14:19', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 51750, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"4500.000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"2250.000\"}]', 1, 1),
(20, '1', '000020', 1, 70000, 80505, 0, NULL, NULL, 10500, 80505, 7, '01:51:49', NULL, 0, 70000, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-26', '2024-12-25 17:29:33', '01:29:33', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 80505, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"7000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3500\"}]', 1, 1),
(21, '1', '000021', 3, 55000, 0, 0, NULL, NULL, 8250, 0, 7, '01:51:59', NULL, 0, 55000, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-26', '2024-12-25 17:51:35', '01:51:35', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 0, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"5500\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"2750\"}]', 1, 1),
(25, '1', '000025', 5, 90000, 103500, 0, NULL, NULL, 13500, 103500, 7, '02:00:46', NULL, 0, 90000, 0, 0, 'service', '5', 0, '', 'fixed', '2024-12-26', '2024-12-25 18:00:31', '02:00:31', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 103500, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"9000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"4500\"}]', 1, 1),
(26, '1', '000026', 3, 70000, 80500, 0, NULL, NULL, 10500, 80500, 7, '02:22:43', NULL, 0, 70000, 0, 0, 'service', '0', 0, '', 'fixed', '2024-12-26', '2024-12-25 18:20:54', '02:20:54', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'Yes', 1, 24, 1, 1, 3, 2, 'Live', 80500, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"7000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3500\"}]', 1, 1),
(27, '1', '000027', 6, 90000, 103500, 0, NULL, NULL, 13500, 103500, 7, '12:53:55', NULL, 0, 90000, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-27', '2024-12-27 04:53:10', '12:53:10', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 103500, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"9000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"4500\"}]', 1, 1),
(28, '1', '000028', 4, 70000, 80500, 0, NULL, NULL, 10500, 80500, 7, '18:17:31', NULL, 0, 70000, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-28', '2024-12-28 10:11:51', '18:11:51', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 1, 'Live', 0, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"7000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3500\"}]', 1, 1),
(29, '1', '000029', 4, 70000, 80500, NULL, NULL, NULL, 10500, 80500, 7, '21:30:23', NULL, 0, 70000, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-28', '2024-12-28 13:20:46', '21:20:46', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 50000, -30500, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"7000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3500\"}]', 1, 1),
(30, '1', '000030', 5, 50000, 57500, 0, NULL, NULL, 7500, 57500, 7, '02:39:59', NULL, 0, 50000, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-29', '2024-12-28 17:47:52', '01:47:52', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 100000, 42500, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"5000\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"2500\"}]', 1, 1),
(31, '1', '000031', 2, 45000, 51750, 0, NULL, NULL, 6750, 51750, 7, '02:41:59', NULL, 0, 45000, 0, 0, 'service', '', 0, '', 'fixed', '2024-12-29', '2024-12-28 18:41:00', '02:41:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 1000000, 948250, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"4500\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"2250\"}]', 1, 1),
(32, '1', '000032', 3, 55000, 63250, 0, NULL, NULL, 8250, 63250, 7, '00:53:55', NULL, 0, 55000, 0, 0, 'service', '', 0, '', 'fixed', '2025-02-17', '2025-02-16 16:53:48', '00:53:48', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 3, 2, 'Live', 63250, 0, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"5500\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"2750\"}]', 1, 1),
(33, '1', '000033', 3, 65000, NULL, NULL, NULL, NULL, 9750, 74750, NULL, '00:00:00', NULL, 0, 65000, 0, 0, 'service', '', 0, '', 'fixed', '2025-02-17', '2025-02-16 17:02:49', '01:02:49', '0000-00-00 00:00:00', '2025-03-08 10:04:33', 'No', 1, 24, 1, 1, 1, 2, 'Live', NULL, NULL, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"6500\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"3250\"}]', 1, 2),
(34, '1', '000034', 1, 35000, NULL, NULL, NULL, NULL, 5250, 40250, NULL, '00:00:00', NULL, 0, 35000, 0, 0, 'service', '', 0, '', 'fixed', '2025-03-07', '2025-03-07 05:55:16', '13:55:16', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 1, 2, 'Live', NULL, NULL, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"1750\"}]', 1, 2),
(35, '1', '000035', 9, 164000, NULL, NULL, NULL, NULL, 24600, 188600, NULL, '00:00:00', NULL, 0, 164000, 0, 0, 'service', '', 0, '', 'fixed', '2025-03-07', '2025-03-07 05:55:35', '13:55:35', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'No', 1, 24, 1, 1, 1, 2, 'Live', NULL, NULL, '[{\"tax_field_id\":\"1\",\"tax_field_type\":\"PPN\",\"tax_field_amount\":\"16400\"},{\"tax_field_id\":\"1\",\"tax_field_type\":\"Service\",\"tax_field_amount\":\"8200\"}]', 1, 2);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sales_details`
--

CREATE TABLE `tbl_sales_details` (
  `id` bigint(50) NOT NULL,
  `food_menu_id` int(10) DEFAULT NULL,
  `menu_name` varchar(50) DEFAULT NULL,
  `qty` int(10) DEFAULT NULL,
  `tmp_qty` int(11) DEFAULT NULL,
  `menu_price_without_discount` float NOT NULL,
  `menu_price_with_discount` float NOT NULL,
  `menu_unit_price` float NOT NULL,
  `menu_vat_percentage` float NOT NULL,
  `menu_taxes` text,
  `menu_discount_value` varchar(20) DEFAULT NULL,
  `discount_type` varchar(20) NOT NULL,
  `menu_note` varchar(150) DEFAULT NULL,
  `discount_amount` double DEFAULT NULL,
  `item_type` varchar(50) DEFAULT NULL,
  `cooking_status` varchar(30) DEFAULT NULL,
  `cooking_start_time` datetime NOT NULL,
  `cooking_done_time` datetime NOT NULL,
  `previous_id` bigint(50) NOT NULL,
  `sales_id` int(10) DEFAULT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order,2=invoiced order, 3=closed order',
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_sales_details`
--

INSERT INTO `tbl_sales_details` (`id`, `food_menu_id`, `menu_name`, `qty`, `tmp_qty`, `menu_price_without_discount`, `menu_price_with_discount`, `menu_unit_price`, `menu_vat_percentage`, `menu_taxes`, `menu_discount_value`, `discount_type`, `menu_note`, `discount_amount`, `item_type`, `cooking_status`, `cooking_start_time`, `cooking_done_time`, `previous_id`, `sales_id`, `order_status`, `user_id`, `outlet_id`, `del_status`) VALUES
(18, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"5.250\",\"item_vat_amount_for_all_quantity\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 18, 9, 0, 1, 1, 'Live'),
(22, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"5.250\",\"item_vat_amount_for_all_quantity\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 22, 13, 0, 1, 1, 'Live'),
(23, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"5.250\",\"item_vat_amount_for_all_quantity\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 23, 14, 0, 1, 1, 'Live'),
(24, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"5.250\",\"item_vat_amount_for_all_quantity\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 24, 15, 0, 1, 1, 'Live'),
(25, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"5.250\",\"item_vat_amount_for_all_quantity\":\"5.250\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 25, 16, 0, 1, 1, 'Live'),
(26, 4, 'Sosis Goreng (04)', 1, 1, 10, 10, 10, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"1.500\",\"item_vat_amount_for_all_quantity\":\"1.500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":0,\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":0,\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 26, 16, 0, 1, 1, 'Live'),
(27, 5, 'Spring Roll Basah (05)', 1, 1, 10, 10, 10, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"1.500\",\"item_vat_amount_for_all_quantity\":\"1.500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 27, 17, 0, 1, 1, 'Live'),
(28, 3, 'Kentang Goreng (03)', 1, 1, 10, 10, 10, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"1.500\",\"item_vat_amount_for_all_quantity\":\"1.500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":0,\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":0,\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 28, 17, 0, 1, 1, 'Live'),
(29, 14, 'Udang Asam Manis  (14)', 1, 1, 25, 25, 25, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"VAT\",\"tax_field_percentage\":\"15\",\"item_vat_amount_for_unit_item\":\"3.750\",\"item_vat_amount_for_all_quantity\":\"3.750\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"SD\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"IGST\",\"tax_field_percentage\":\"0\",\"item_vat_amount_for_unit_item\":\"0.000\",\"item_vat_amount_for_all_quantity\":\"0.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 29, 17, 0, 1, 1, 'Live'),
(30, 4, 'Sosis Goreng (04)', 1, 1, 10, 10, 10, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1.000\",\"item_vat_amount_for_all_quantity\":\"1.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"0.500\",\"item_vat_amount_for_all_quantity\":\"0.500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 30, 18, 0, 1, 1, 'Live'),
(31, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35, 35, 35, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3.500\",\"item_vat_amount_for_all_quantity\":\"3.500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1.750\",\"item_vat_amount_for_all_quantity\":\"1.750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 31, 18, 0, 1, 1, 'Live'),
(32, 14, 'Udang Asam Manis  (14)', 1, 1, 25, 25, 25, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2.500\",\"item_vat_amount_for_all_quantity\":\"2.500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1.250\",\"item_vat_amount_for_all_quantity\":\"1.250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 32, 18, 0, 1, 1, 'Live'),
(33, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500.000\",\"item_vat_amount_for_all_quantity\":\"3500.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750.000\",\"item_vat_amount_for_all_quantity\":\"1750.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 33, 19, 0, 1, 1, 'Live'),
(34, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000.000\",\"item_vat_amount_for_all_quantity\":\"1000.000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500.000\",\"item_vat_amount_for_all_quantity\":\"500.000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 34, 19, 0, 1, 1, 'Live'),
(35, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 2, 2, 70000, 70000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 35, 20, 0, 1, 1, 'Live'),
(36, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 36, 21, 0, 1, 1, 'Live'),
(37, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 37, 21, 0, 1, 1, 'Live'),
(38, 5, 'Spring Roll Basah (05)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 38, 21, 0, 1, 1, 'Live'),
(49, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 49, 25, 0, 1, 1, 'Live'),
(50, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 50, 25, 0, 1, 1, 'Live'),
(51, 14, 'Udang Asam Manis  (14)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 51, 25, 0, 1, 1, 'Live'),
(52, 4, 'Sosis Goreng (04)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 52, 25, 0, 1, 1, 'Live'),
(53, 9, 'Indomie Goreng  (09)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 53, 25, 0, 1, 1, 'Live'),
(64, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 64, 26, 0, 1, 1, 'Live'),
(65, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 65, 26, 0, 1, 1, 'Live'),
(66, 14, 'Udang Asam Manis  (14)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 66, 26, 0, 1, 1, 'Live'),
(67, 4, 'Sosis Goreng (04)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 67, 27, 0, 1, 1, 'Live'),
(68, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 68, 27, 0, 1, 1, 'Live'),
(69, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 69, 27, 0, 1, 1, 'Live'),
(70, 5, 'Spring Roll Basah (05)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 70, 27, 0, 1, 1, 'Live'),
(71, 2, 'Crispy Cumi With Tar Tar Sauce  (02)', 1, 1, 15000, 15000, 15000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"750\",\"item_vat_amount_for_all_quantity\":\"750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 71, 27, 0, 1, 1, 'Live'),
(72, 10, 'Indomie Kuah (10)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 72, 27, 0, 1, 1, 'Live'),
(73, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 73, 28, 0, 1, 1, 'Live'),
(74, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 74, 28, 0, 1, 1, 'Live'),
(75, 2, 'Crispy Cumi With Tar Tar Sauce  (02)', 1, 1, 15000, 15000, 15000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"750\",\"item_vat_amount_for_all_quantity\":\"750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 75, 28, 0, 1, 1, 'Live'),
(76, 5, 'Spring Roll Basah (05)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 76, 28, 0, 1, 1, 'Live'),
(77, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 77, 29, 0, 1, 1, 'Live'),
(78, 4, 'Sosis Goreng (04)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 78, 29, 0, 1, 1, 'Live'),
(79, 2, 'Crispy Cumi With Tar Tar Sauce  (02)', 1, 1, 15000, 15000, 15000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"750\",\"item_vat_amount_for_all_quantity\":\"750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 79, 29, 0, 1, 1, 'Live'),
(80, 3, 'Kentang Goreng (03)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 80, 29, 0, 1, 1, 'Live'),
(81, 5, 'Spring Roll Basah (05)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 81, 30, 0, 1, 1, 'Live'),
(82, 10, 'Indomie Kuah (10)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 82, 30, 0, 1, 1, 'Live'),
(83, 10, 'Indomie Kuah (10)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 83, 30, 0, 1, 1, 'Live'),
(84, 9, 'Indomie Goreng  (09)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 84, 30, 0, 1, 1, 'Live'),
(85, 9, 'Indomie Goreng  (09)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 85, 30, 0, 1, 1, 'Live'),
(86, 5, 'Spring Roll Basah (05)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 86, 31, 0, 1, 1, 'Live'),
(87, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 87, 31, 0, 1, 1, 'Live'),
(88, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 88, 32, 0, 1, 1, 'Live'),
(89, 4, 'Sosis Goreng (04)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 89, 32, 0, 1, 1, 'Live'),
(90, 10, 'Indomie Kuah (10)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 90, 32, 0, 1, 1, 'Live'),
(91, 11, 'Sapi Lada Hitam  (11)', 1, 1, 30000, 30000, 30000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3000\",\"item_vat_amount_for_all_quantity\":\"3000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', 'Done', '0000-00-00 00:00:00', '2025-03-08 10:04:33', 91, 33, 0, 1, 1, 'Live'),
(92, 10, 'Indomie Kuah (10)', 1, 1, 10000, 10000, 10000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"}]', '0', 'fixed', '', 0, 'Kitchen Item', 'Done', '0000-00-00 00:00:00', '2025-03-08 10:04:33', 92, 33, 0, 1, 1, 'Live'),
(93, 14, 'Udang Asam Manis  (14)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', 'Done', '0000-00-00 00:00:00', '2025-03-08 10:04:33', 93, 33, 0, 1, 1, 'Live'),
(94, 1, 'Sop Ikan Laut &amp; Ikan Goreng Laut (01)', 1, 1, 35000, 35000, 35000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"3500\",\"item_vat_amount_for_all_quantity\":\"3500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1750\",\"item_vat_amount_for_all_quantity\":\"1750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 94, 34, 0, 1, 1, 'Live'),
(95, 12, 'Ayam Lalapan (12)', 1, 1, 20000, 20000, 20000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2000\",\"item_vat_amount_for_all_quantity\":\"2000\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1000\",\"item_vat_amount_for_all_quantity\":\"1000\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 95, 35, 0, 1, 1, 'Live'),
(96, 13, 'Cumi Asam Manis  (13)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 96, 35, 0, 1, 1, 'Live'),
(97, 20, 'Fish &amp; Cheap  (20)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 97, 35, 0, 1, 1, 'Live'),
(98, 19, 'Spaghetti Bolognese (19)', 1, 1, 25000, 25000, 25000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"2500\",\"item_vat_amount_for_all_quantity\":\"2500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"1250\",\"item_vat_amount_for_all_quantity\":\"1250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 98, 35, 0, 1, 1, 'Live'),
(99, 27, 'Ice Cream Sundae (27)', 1, 1, 15000, 15000, 15000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"750\",\"item_vat_amount_for_all_quantity\":\"750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 99, 35, 0, 1, 1, 'Live'),
(100, 47, 'Long Black (47)', 1, 1, 15000, 15000, 15000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1500\",\"item_vat_amount_for_all_quantity\":\"1500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"750\",\"item_vat_amount_for_all_quantity\":\"750\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 100, 35, 0, 1, 1, 'Live'),
(101, 51, 'Iced Mochacino  (51)', 1, 1, 17000, 17000, 17000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1700\",\"item_vat_amount_for_all_quantity\":\"1700\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"850\",\"item_vat_amount_for_all_quantity\":\"850\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 101, 35, 0, 1, 1, 'Live'),
(102, 53, 'Iced Americano (53)', 1, 1, 17000, 17000, 17000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"1700\",\"item_vat_amount_for_all_quantity\":\"1700\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"850\",\"item_vat_amount_for_all_quantity\":\"850\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 102, 35, 0, 1, 1, 'Live'),
(103, 48, 'Coffee Bali  (48)', 1, 1, 5000, 5000, 5000, 0, '[{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"PPN\",\"tax_field_percentage\":\"10\",\"item_vat_amount_for_unit_item\":\"500\",\"item_vat_amount_for_all_quantity\":\"500\"},{\"tax_field_id\":\"1\",\"tax_field_company_id\":\"1\",\"tax_field_name\":\"Service\",\"tax_field_percentage\":\"5\",\"item_vat_amount_for_unit_item\":\"250\",\"item_vat_amount_for_all_quantity\":\"250\"}]', '0', 'fixed', '', 0, 'Kitchen Item', NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 103, 35, 0, 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sales_details_modifiers`
--

CREATE TABLE `tbl_sales_details_modifiers` (
  `id` bigint(50) NOT NULL,
  `modifier_id` int(15) NOT NULL,
  `modifier_price` float NOT NULL,
  `food_menu_id` int(10) NOT NULL,
  `sales_id` int(15) NOT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order,2=invoiced order, 3=closed order',
  `sales_details_id` int(15) NOT NULL,
  `menu_vat_percentage` float DEFAULT NULL,
  `menu_taxes` text,
  `user_id` int(15) NOT NULL,
  `outlet_id` int(15) NOT NULL,
  `customer_id` int(15) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sale_consumptions`
--

CREATE TABLE `tbl_sale_consumptions` (
  `id` bigint(50) NOT NULL,
  `sale_id` int(10) DEFAULT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order,2=invoiced order, 3=closed order',
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_sale_consumptions`
--

INSERT INTO `tbl_sale_consumptions` (`id`, `sale_id`, `order_status`, `user_id`, `outlet_id`, `del_status`) VALUES
(9, 9, 0, 1, 1, 'Live'),
(13, 13, 0, 1, 1, 'Live'),
(14, 14, 0, 1, 1, 'Live'),
(15, 15, 0, 1, 1, 'Live'),
(16, 16, 0, 1, 1, 'Live'),
(17, 17, 0, 1, 1, 'Live'),
(18, 18, 0, 1, 1, 'Live'),
(19, 19, 0, 1, 1, 'Live'),
(20, 20, 0, 1, 1, 'Live'),
(21, 21, 0, 1, 1, 'Live'),
(25, 25, 0, 1, 1, 'Live'),
(29, 26, 0, 1, 1, 'Live'),
(30, 27, 0, 1, 1, 'Live'),
(31, 28, 0, 1, 1, 'Live'),
(32, 29, 0, 1, 1, 'Live'),
(33, 30, 0, 1, 1, 'Live'),
(34, 31, 0, 1, 1, 'Live'),
(35, 32, 0, 1, 1, 'Live'),
(36, 33, 0, 1, 1, 'Live'),
(37, 34, 0, 1, 1, 'Live'),
(38, 35, 0, 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sale_consumptions_of_menus`
--

CREATE TABLE `tbl_sale_consumptions_of_menus` (
  `id` bigint(50) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `consumption` float DEFAULT NULL,
  `sale_consumption_id` int(10) DEFAULT NULL,
  `sales_id` int(10) NOT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order,2=invoiced order, 3=closed order',
  `food_menu_id` int(10) NOT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sale_consumptions_of_modifiers_of_menus`
--

CREATE TABLE `tbl_sale_consumptions_of_modifiers_of_menus` (
  `id` bigint(50) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `consumption` float DEFAULT NULL,
  `sale_consumption_id` int(10) DEFAULT NULL,
  `sales_id` int(10) NOT NULL,
  `order_status` tinyint(1) NOT NULL COMMENT '1=new order,2=invoiced order, 3=closed order',
  `food_menu_id` int(10) NOT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_sessions`
--

CREATE TABLE `tbl_sessions` (
  `id` varchar(128) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `data` blob NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_sessions`
--

INSERT INTO `tbl_sessions` (`id`, `ip_address`, `timestamp`, `data`) VALUES
('6p9u7dcljtha1huonvqc2u82k89pkbuc', '::1', 1741413577, 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),
('bpkao8dvc0426rtpph0mvb3nekaaean7', '::1', 1741413683, 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);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_settings`
--

CREATE TABLE `tbl_settings` (
  `id` int(11) NOT NULL,
  `site_name` varchar(300) DEFAULT NULL,
  `footer` varchar(300) DEFAULT NULL,
  `system_logo` text,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) NOT NULL DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf32;

--
-- Dumping data for table `tbl_settings`
--

INSERT INTO `tbl_settings` (`id`, `site_name`, `footer`, `system_logo`, `company_id`, `del_status`) VALUES
(4, 'iRestora PLUS - Next Gen Restaurant POS', 'iRestora PLUS - Next Gen Restaurant POS', '93ddd1cfd25f29986c3815608532212f.png', 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_suppliers`
--

CREATE TABLE `tbl_suppliers` (
  `id` int(10) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `contact_person` varchar(50) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(300) DEFAULT NULL,
  `description` varchar(500) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_supplier_payments`
--

CREATE TABLE `tbl_supplier_payments` (
  `id` int(10) NOT NULL,
  `date` date DEFAULT NULL,
  `supplier_id` int(11) DEFAULT NULL,
  `amount` float DEFAULT NULL,
  `note` varchar(200) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_tables`
--

CREATE TABLE `tbl_tables` (
  `id` int(10) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `sit_capacity` varchar(50) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `description` varchar(100) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_tables`
--

INSERT INTO `tbl_tables` (`id`, `name`, `sit_capacity`, `position`, `description`, `user_id`, `outlet_id`, `company_id`, `del_status`) VALUES
(1, '1', '9', 'Joglo', '', 1, 1, 1, 'Live'),
(2, '2', '9', 'Joglo', '', 1, 1, 1, 'Live'),
(3, '3', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(4, '4', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(5, '5', '2', 'Joglo', '', 1, 1, 1, 'Live'),
(6, '6', '2', 'Joglo', '', 1, 1, 1, 'Live'),
(7, '7', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(8, '8', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(9, '8', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(10, '9', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(11, '10', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(12, '11', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(13, '12', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(14, '13', '4', 'Joglo', '', 1, 1, 1, 'Live'),
(15, '14', '2', 'Joglo', '', 1, 1, 1, 'Live'),
(16, '15', '2', 'Joglo', '', 1, 1, 1, 'Live'),
(17, '16', '4', 'Sekepat', '', 1, 1, 1, 'Live'),
(18, '17', '4', 'Sekepat', '', 1, 1, 1, 'Live'),
(19, '18', '4', 'Sekepat', '', 1, 1, 1, 'Live'),
(20, '19', '4', 'Meja Payung', '', 1, 1, 1, 'Live'),
(21, '20', '4', 'Meja Payung', '', 1, 1, 1, 'Live'),
(22, '21', '4', 'Meja Payung', '', 1, 1, 1, 'Live'),
(23, '22', '6', 'Meja Kayu (barat kolam)', '', 1, 1, 1, 'Live'),
(24, '23', '6', 'Meja Kayu (barat kolam)', '', 1, 1, 1, 'Live'),
(25, '24', '6', 'Meja Kayu (barat kolam)', '', 1, 1, 1, 'Live'),
(26, '25', '2', 'Meja Kayu (barat kolam)', '', 1, 1, 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_temp_kot`
--

CREATE TABLE `tbl_temp_kot` (
  `id` int(10) NOT NULL,
  `temp_kot_info` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_time_zone`
--

CREATE TABLE `tbl_time_zone` (
  `id` int(10) NOT NULL,
  `country_code` varchar(2) DEFAULT NULL,
  `zone_name` varchar(35) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf32;

--
-- Dumping data for table `tbl_time_zone`
--

INSERT INTO `tbl_time_zone` (`id`, `country_code`, `zone_name`, `del_status`) VALUES
(1, 'AD', 'Europe/Andorra', 'Live'),
(2, 'AE', 'Asia/Dubai', 'Live'),
(3, 'AF', 'Asia/Kabul', 'Live'),
(4, 'AG', 'America/Antigua', 'Live'),
(5, 'AI', 'America/Anguilla', 'Live'),
(6, 'AL', 'Europe/Tirane', 'Live'),
(7, 'AM', 'Asia/Yerevan', 'Live'),
(8, 'AO', 'Africa/Luanda', 'Live'),
(9, 'AQ', 'Antarctica/McMurdo', 'Live'),
(10, 'AQ', 'Antarctica/Casey', 'Live'),
(11, 'AQ', 'Antarctica/Davis', 'Live'),
(12, 'AQ', 'Antarctica/DumontDUrville', 'Live'),
(13, 'AQ', 'Antarctica/Mawson', 'Live'),
(14, 'AQ', 'Antarctica/Palmer', 'Live'),
(15, 'AQ', 'Antarctica/Rothera', 'Live'),
(16, 'AQ', 'Antarctica/Syowa', 'Live'),
(17, 'AQ', 'Antarctica/Troll', 'Live'),
(18, 'AQ', 'Antarctica/Vostok', 'Live'),
(19, 'AR', 'America/Argentina/Buenos_Aires', 'Live'),
(20, 'AR', 'America/Argentina/Cordoba', 'Live'),
(21, 'AR', 'America/Argentina/Salta', 'Live'),
(22, 'AR', 'America/Argentina/Jujuy', 'Live'),
(23, 'AR', 'America/Argentina/Tucuman', 'Live'),
(24, 'AR', 'America/Argentina/Catamarca', 'Live'),
(25, 'AR', 'America/Argentina/La_Rioja', 'Live'),
(26, 'AR', 'America/Argentina/San_Juan', 'Live'),
(27, 'AR', 'America/Argentina/Mendoza', 'Live'),
(28, 'AR', 'America/Argentina/San_Luis', 'Live'),
(29, 'AR', 'America/Argentina/Rio_Gallegos', 'Live'),
(30, 'AR', 'America/Argentina/Ushuaia', 'Live'),
(31, 'AS', 'Pacific/Pago_Pago', 'Live'),
(32, 'AT', 'Europe/Vienna', 'Live'),
(33, 'AU', 'Australia/Lord_Howe', 'Live'),
(34, 'AU', 'Antarctica/Macquarie', 'Live'),
(35, 'AU', 'Australia/Hobart', 'Live'),
(36, 'AU', 'Australia/Currie', 'Live'),
(37, 'AU', 'Australia/Melbourne', 'Live'),
(38, 'AU', 'Australia/Sydney', 'Live'),
(39, 'AU', 'Australia/Broken_Hill', 'Live'),
(40, 'AU', 'Australia/Brisbane', 'Live'),
(41, 'AU', 'Australia/Lindeman', 'Live'),
(42, 'AU', 'Australia/Adelaide', 'Live'),
(43, 'AU', 'Australia/Darwin', 'Live'),
(44, 'AU', 'Australia/Perth', 'Live'),
(45, 'AU', 'Australia/Eucla', 'Live'),
(46, 'AW', 'America/Aruba', 'Live'),
(47, 'AX', 'Europe/Mariehamn', 'Live'),
(48, 'AZ', 'Asia/Baku', 'Live'),
(49, 'BA', 'Europe/Sarajevo', 'Live'),
(50, 'BB', 'America/Barbados', 'Live'),
(51, 'BD', 'Asia/Dhaka', 'Live'),
(52, 'BE', 'Europe/Brussels', 'Live'),
(53, 'BF', 'Africa/Ouagadougou', 'Live'),
(54, 'BG', 'Europe/Sofia', 'Live'),
(55, 'BH', 'Asia/Bahrain', 'Live'),
(56, 'BI', 'Africa/Bujumbura', 'Live'),
(57, 'BJ', 'Africa/Porto-Novo', 'Live'),
(58, 'BL', 'America/St_Barthelemy', 'Live'),
(59, 'BM', 'Atlantic/Bermuda', 'Live'),
(60, 'BN', 'Asia/Brunei', 'Live'),
(61, 'BO', 'America/La_Paz', 'Live'),
(62, 'BQ', 'America/Kralendijk', 'Live'),
(63, 'BR', 'America/Noronha', 'Live'),
(64, 'BR', 'America/Belem', 'Live'),
(65, 'BR', 'America/Fortaleza', 'Live'),
(66, 'BR', 'America/Recife', 'Live'),
(67, 'BR', 'America/Araguaina', 'Live'),
(68, 'BR', 'America/Maceio', 'Live'),
(69, 'BR', 'America/Bahia', 'Live'),
(70, 'BR', 'America/Sao_Paulo', 'Live'),
(71, 'BR', 'America/Campo_Grande', 'Live'),
(72, 'BR', 'America/Cuiaba', 'Live'),
(73, 'BR', 'America/Santarem', 'Live'),
(74, 'BR', 'America/Porto_Velho', 'Live'),
(75, 'BR', 'America/Boa_Vista', 'Live'),
(76, 'BR', 'America/Manaus', 'Live'),
(77, 'BR', 'America/Eirunepe', 'Live'),
(78, 'BR', 'America/Rio_Branco', 'Live'),
(79, 'BS', 'America/Nassau', 'Live'),
(80, 'BT', 'Asia/Thimphu', 'Live'),
(81, 'BW', 'Africa/Gaborone', 'Live'),
(82, 'BY', 'Europe/Minsk', 'Live'),
(83, 'BZ', 'America/Belize', 'Live'),
(84, 'CA', 'America/St_Johns', 'Live'),
(85, 'CA', 'America/Halifax', 'Live'),
(86, 'CA', 'America/Glace_Bay', 'Live'),
(87, 'CA', 'America/Moncton', 'Live'),
(88, 'CA', 'America/Goose_Bay', 'Live'),
(89, 'CA', 'America/Blanc-Sablon', 'Live'),
(90, 'CA', 'America/Toronto', 'Live'),
(91, 'CA', 'America/Nipigon', 'Live'),
(92, 'CA', 'America/Thunder_Bay', 'Live'),
(93, 'CA', 'America/Iqaluit', 'Live'),
(94, 'CA', 'America/Pangnirtung', 'Live'),
(95, 'CA', 'America/Atikokan', 'Live'),
(96, 'CA', 'America/Winnipeg', 'Live'),
(97, 'CA', 'America/Rainy_River', 'Live'),
(98, 'CA', 'America/Resolute', 'Live'),
(99, 'CA', 'America/Rankin_Inlet', 'Live'),
(100, 'CA', 'America/Regina', 'Live'),
(101, 'CA', 'America/Swift_Current', 'Live'),
(102, 'CA', 'America/Edmonton', 'Live'),
(103, 'CA', 'America/Cambridge_Bay', 'Live'),
(104, 'CA', 'America/Yellowknife', 'Live'),
(105, 'CA', 'America/Inuvik', 'Live'),
(106, 'CA', 'America/Creston', 'Live'),
(107, 'CA', 'America/Dawson_Creek', 'Live'),
(108, 'CA', 'America/Fort_Nelson', 'Live'),
(109, 'CA', 'America/Vancouver', 'Live'),
(110, 'CA', 'America/Whitehorse', 'Live'),
(111, 'CA', 'America/Dawson', 'Live'),
(112, 'CC', 'Indian/Cocos', 'Live'),
(113, 'CD', 'Africa/Kinshasa', 'Live'),
(114, 'CD', 'Africa/Lubumbashi', 'Live'),
(115, 'CF', 'Africa/Bangui', 'Live'),
(116, 'CG', 'Africa/Brazzaville', 'Live'),
(117, 'CH', 'Europe/Zurich', 'Live'),
(118, 'CI', 'Africa/Abidjan', 'Live'),
(119, 'CK', 'Pacific/Rarotonga', 'Live'),
(120, 'CL', 'America/Santiago', 'Live'),
(121, 'CL', 'America/Punta_Arenas', 'Live'),
(122, 'CL', 'Pacific/Easter', 'Live'),
(123, 'CM', 'Africa/Douala', 'Live'),
(124, 'CN', 'Asia/Shanghai', 'Live'),
(125, 'CN', 'Asia/Urumqi', 'Live'),
(126, 'CO', 'America/Bogota', 'Live'),
(127, 'CR', 'America/Costa_Rica', 'Live'),
(128, 'CU', 'America/Havana', 'Live'),
(129, 'CV', 'Atlantic/Cape_Verde', 'Live'),
(130, 'CW', 'America/Curacao', 'Live'),
(131, 'CX', 'Indian/Christmas', 'Live'),
(132, 'CY', 'Asia/Nicosia', 'Live'),
(133, 'CY', 'Asia/Famagusta', 'Live'),
(134, 'CZ', 'Europe/Prague', 'Live'),
(135, 'DE', 'Europe/Berlin', 'Live'),
(136, 'DE', 'Europe/Busingen', 'Live'),
(137, 'DJ', 'Africa/Djibouti', 'Live'),
(138, 'DK', 'Europe/Copenhagen', 'Live'),
(139, 'DM', 'America/Dominica', 'Live'),
(140, 'DO', 'America/Santo_Domingo', 'Live'),
(141, 'DZ', 'Africa/Algiers', 'Live'),
(142, 'EC', 'America/Guayaquil', 'Live'),
(143, 'EC', 'Pacific/Galapagos', 'Live'),
(144, 'EE', 'Europe/Tallinn', 'Live'),
(145, 'EG', 'Africa/Cairo', 'Live'),
(146, 'EH', 'Africa/El_Aaiun', 'Live'),
(147, 'ER', 'Africa/Asmara', 'Live'),
(148, 'ES', 'Europe/Madrid', 'Live'),
(149, 'ES', 'Africa/Ceuta', 'Live'),
(150, 'ES', 'Atlantic/Canary', 'Live'),
(151, 'ET', 'Africa/Addis_Ababa', 'Live'),
(152, 'FI', 'Europe/Helsinki', 'Live'),
(153, 'FJ', 'Pacific/Fiji', 'Live'),
(154, 'FK', 'Atlantic/Stanley', 'Live'),
(155, 'FM', 'Pacific/Chuuk', 'Live'),
(156, 'FM', 'Pacific/Pohnpei', 'Live'),
(157, 'FM', 'Pacific/Kosrae', 'Live'),
(158, 'FO', 'Atlantic/Faroe', 'Live'),
(159, 'FR', 'Europe/Paris', 'Live'),
(160, 'GA', 'Africa/Libreville', 'Live'),
(161, 'GB', 'Europe/London', 'Live'),
(162, 'GD', 'America/Grenada', 'Live'),
(163, 'GE', 'Asia/Tbilisi', 'Live'),
(164, 'GF', 'America/Cayenne', 'Live'),
(165, 'GG', 'Europe/Guernsey', 'Live'),
(166, 'GH', 'Africa/Accra', 'Live'),
(167, 'GI', 'Europe/Gibraltar', 'Live'),
(168, 'GL', 'America/Godthab', 'Live'),
(169, 'GL', 'America/Danmarkshavn', 'Live'),
(170, 'GL', 'America/Scoresbysund', 'Live'),
(171, 'GL', 'America/Thule', 'Live'),
(172, 'GM', 'Africa/Banjul', 'Live'),
(173, 'GN', 'Africa/Conakry', 'Live'),
(174, 'GP', 'America/Guadeloupe', 'Live'),
(175, 'GQ', 'Africa/Malabo', 'Live'),
(176, 'GR', 'Europe/Athens', 'Live'),
(177, 'GS', 'Atlantic/South_Georgia', 'Live'),
(178, 'GT', 'America/Guatemala', 'Live'),
(179, 'GU', 'Pacific/Guam', 'Live'),
(180, 'GW', 'Africa/Bissau', 'Live'),
(181, 'GY', 'America/Guyana', 'Live'),
(182, 'HK', 'Asia/Hong_Kong', 'Live'),
(183, 'HN', 'America/Tegucigalpa', 'Live'),
(184, 'HR', 'Europe/Zagreb', 'Live'),
(185, 'HT', 'America/Port-au-Prince', 'Live'),
(186, 'HU', 'Europe/Budapest', 'Live'),
(187, 'ID', 'Asia/Jakarta', 'Live'),
(188, 'ID', 'Asia/Pontianak', 'Live'),
(189, 'ID', 'Asia/Makassar', 'Live'),
(190, 'ID', 'Asia/Jayapura', 'Live'),
(191, 'IE', 'Europe/Dublin', 'Live'),
(192, 'IL', 'Asia/Jerusalem', 'Live'),
(193, 'IM', 'Europe/Isle_of_Man', 'Live'),
(194, 'IN', 'Asia/Kolkata', 'Live'),
(195, 'IO', 'Indian/Chagos', 'Live'),
(196, 'IQ', 'Asia/Baghdad', 'Live'),
(197, 'IR', 'Asia/Tehran', 'Live'),
(198, 'IS', 'Atlantic/Reykjavik', 'Live'),
(199, 'IT', 'Europe/Rome', 'Live'),
(200, 'JE', 'Europe/Jersey', 'Live'),
(201, 'JM', 'America/Jamaica', 'Live'),
(202, 'JO', 'Asia/Amman', 'Live'),
(203, 'JP', 'Asia/Tokyo', 'Live'),
(204, 'KE', 'Africa/Nairobi', 'Live'),
(205, 'KG', 'Asia/Bishkek', 'Live'),
(206, 'KH', 'Asia/Phnom_Penh', 'Live'),
(207, 'KI', 'Pacific/Tarawa', 'Live'),
(208, 'KI', 'Pacific/Enderbury', 'Live'),
(209, 'KI', 'Pacific/Kiritimati', 'Live'),
(210, 'KM', 'Indian/Comoro', 'Live'),
(211, 'KN', 'America/St_Kitts', 'Live'),
(212, 'KP', 'Asia/Pyongyang', 'Live'),
(213, 'KR', 'Asia/Seoul', 'Live'),
(214, 'KW', 'Asia/Kuwait', 'Live'),
(215, 'KY', 'America/Cayman', 'Live'),
(216, 'KZ', 'Asia/Almaty', 'Live'),
(217, 'KZ', 'Asia/Qyzylorda', 'Live'),
(218, 'KZ', 'Asia/Aqtobe', 'Live'),
(219, 'KZ', 'Asia/Aqtau', 'Live'),
(220, 'KZ', 'Asia/Atyrau', 'Live'),
(221, 'KZ', 'Asia/Oral', 'Live'),
(222, 'LA', 'Asia/Vientiane', 'Live'),
(223, 'LB', 'Asia/Beirut', 'Live'),
(224, 'LC', 'America/St_Lucia', 'Live'),
(225, 'LI', 'Europe/Vaduz', 'Live'),
(226, 'LK', 'Asia/Colombo', 'Live'),
(227, 'LR', 'Africa/Monrovia', 'Live'),
(228, 'LS', 'Africa/Maseru', 'Live'),
(229, 'LT', 'Europe/Vilnius', 'Live'),
(230, 'LU', 'Europe/Luxembourg', 'Live'),
(231, 'LV', 'Europe/Riga', 'Live'),
(232, 'LY', 'Africa/Tripoli', 'Live'),
(233, 'MA', 'Africa/Casablanca', 'Live'),
(234, 'MC', 'Europe/Monaco', 'Live'),
(235, 'MD', 'Europe/Chisinau', 'Live'),
(236, 'ME', 'Europe/Podgorica', 'Live'),
(237, 'MF', 'America/Marigot', 'Live'),
(238, 'MG', 'Indian/Antananarivo', 'Live'),
(239, 'MH', 'Pacific/Majuro', 'Live'),
(240, 'MH', 'Pacific/Kwajalein', 'Live'),
(241, 'MK', 'Europe/Skopje', 'Live'),
(242, 'ML', 'Africa/Bamako', 'Live'),
(243, 'MM', 'Asia/Yangon', 'Live'),
(244, 'MN', 'Asia/Ulaanbaatar', 'Live'),
(245, 'MN', 'Asia/Hovd', 'Live'),
(246, 'MN', 'Asia/Choibalsan', 'Live'),
(247, 'MO', 'Asia/Macau', 'Live'),
(248, 'MP', 'Pacific/Saipan', 'Live'),
(249, 'MQ', 'America/Martinique', 'Live'),
(250, 'MR', 'Africa/Nouakchott', 'Live'),
(251, 'MS', 'America/Montserrat', 'Live'),
(252, 'MT', 'Europe/Malta', 'Live'),
(253, 'MU', 'Indian/Mauritius', 'Live'),
(254, 'MV', 'Indian/Maldives', 'Live'),
(255, 'MW', 'Africa/Blantyre', 'Live'),
(256, 'MX', 'America/Mexico_City', 'Live'),
(257, 'MX', 'America/Cancun', 'Live'),
(258, 'MX', 'America/Merida', 'Live'),
(259, 'MX', 'America/Monterrey', 'Live'),
(260, 'MX', 'America/Matamoros', 'Live'),
(261, 'MX', 'America/Mazatlan', 'Live'),
(262, 'MX', 'America/Chihuahua', 'Live'),
(263, 'MX', 'America/Ojinaga', 'Live'),
(264, 'MX', 'America/Hermosillo', 'Live'),
(265, 'MX', 'America/Tijuana', 'Live'),
(266, 'MX', 'America/Bahia_Banderas', 'Live'),
(267, 'MY', 'Asia/Kuala_Lumpur', 'Live'),
(268, 'MY', 'Asia/Kuching', 'Live'),
(269, 'MZ', 'Africa/Maputo', 'Live'),
(270, 'NA', 'Africa/Windhoek', 'Live'),
(271, 'NC', 'Pacific/Noumea', 'Live'),
(272, 'NE', 'Africa/Niamey', 'Live'),
(273, 'NF', 'Pacific/Norfolk', 'Live'),
(274, 'NG', 'Africa/Lagos', 'Live'),
(275, 'NI', 'America/Managua', 'Live'),
(276, 'NL', 'Europe/Amsterdam', 'Live'),
(277, 'NO', 'Europe/Oslo', 'Live'),
(278, 'NP', 'Asia/Kathmandu', 'Live'),
(279, 'NR', 'Pacific/Nauru', 'Live'),
(280, 'NU', 'Pacific/Niue', 'Live'),
(281, 'NZ', 'Pacific/Auckland', 'Live'),
(282, 'NZ', 'Pacific/Chatham', 'Live'),
(283, 'OM', 'Asia/Muscat', 'Live'),
(284, 'PA', 'America/Panama', 'Live'),
(285, 'PE', 'America/Lima', 'Live'),
(286, 'PF', 'Pacific/Tahiti', 'Live'),
(287, 'PF', 'Pacific/Marquesas', 'Live'),
(288, 'PF', 'Pacific/Gambier', 'Live'),
(289, 'PG', 'Pacific/Port_Moresby', 'Live'),
(290, 'PG', 'Pacific/Bougainville', 'Live'),
(291, 'PH', 'Asia/Manila', 'Live'),
(292, 'PK', 'Asia/Karachi', 'Live'),
(293, 'PL', 'Europe/Warsaw', 'Live'),
(294, 'PM', 'America/Miquelon', 'Live'),
(295, 'PN', 'Pacific/Pitcairn', 'Live'),
(296, 'PR', 'America/Puerto_Rico', 'Live'),
(297, 'PS', 'Asia/Gaza', 'Live'),
(298, 'PS', 'Asia/Hebron', 'Live'),
(299, 'PT', 'Europe/Lisbon', 'Live'),
(300, 'PT', 'Atlantic/Madeira', 'Live'),
(301, 'PT', 'Atlantic/Azores', 'Live'),
(302, 'PW', 'Pacific/Palau', 'Live'),
(303, 'PY', 'America/Asuncion', 'Live'),
(304, 'QA', 'Asia/Qatar', 'Live'),
(305, 'RE', 'Indian/Reunion', 'Live'),
(306, 'RO', 'Europe/Bucharest', 'Live'),
(307, 'RS', 'Europe/Belgrade', 'Live'),
(308, 'RU', 'Europe/Kaliningrad', 'Live'),
(309, 'RU', 'Europe/Moscow', 'Live'),
(310, 'RU', 'Europe/Simferopol', 'Live'),
(311, 'RU', 'Europe/Volgograd', 'Live'),
(312, 'RU', 'Europe/Kirov', 'Live'),
(313, 'RU', 'Europe/Astrakhan', 'Live'),
(314, 'RU', 'Europe/Saratov', 'Live'),
(315, 'RU', 'Europe/Ulyanovsk', 'Live'),
(316, 'RU', 'Europe/Samara', 'Live'),
(317, 'RU', 'Asia/Yekaterinburg', 'Live'),
(318, 'RU', 'Asia/Omsk', 'Live'),
(319, 'RU', 'Asia/Novosibirsk', 'Live'),
(320, 'RU', 'Asia/Barnaul', 'Live'),
(321, 'RU', 'Asia/Tomsk', 'Live'),
(322, 'RU', 'Asia/Novokuznetsk', 'Live'),
(323, 'RU', 'Asia/Krasnoyarsk', 'Live'),
(324, 'RU', 'Asia/Irkutsk', 'Live'),
(325, 'RU', 'Asia/Chita', 'Live'),
(326, 'RU', 'Asia/Yakutsk', 'Live'),
(327, 'RU', 'Asia/Khandyga', 'Live'),
(328, 'RU', 'Asia/Vladivostok', 'Live'),
(329, 'RU', 'Asia/Ust-Nera', 'Live'),
(330, 'RU', 'Asia/Magadan', 'Live'),
(331, 'RU', 'Asia/Sakhalin', 'Live'),
(332, 'RU', 'Asia/Srednekolymsk', 'Live'),
(333, 'RU', 'Asia/Kamchatka', 'Live'),
(334, 'RU', 'Asia/Anadyr', 'Live'),
(335, 'RW', 'Africa/Kigali', 'Live'),
(336, 'SA', 'Asia/Riyadh', 'Live'),
(337, 'SB', 'Pacific/Guadalcanal', 'Live'),
(338, 'SC', 'Indian/Mahe', 'Live'),
(339, 'SD', 'Africa/Khartoum', 'Live'),
(340, 'SE', 'Europe/Stockholm', 'Live'),
(341, 'SG', 'Asia/Singapore', 'Live'),
(342, 'SH', 'Atlantic/St_Helena', 'Live'),
(343, 'SI', 'Europe/Ljubljana', 'Live'),
(344, 'SJ', 'Arctic/Longyearbyen', 'Live'),
(345, 'SK', 'Europe/Bratislava', 'Live'),
(346, 'SL', 'Africa/Freetown', 'Live'),
(347, 'SM', 'Europe/San_Marino', 'Live'),
(348, 'SN', 'Africa/Dakar', 'Live'),
(349, 'SO', 'Africa/Mogadishu', 'Live'),
(350, 'SR', 'America/Paramaribo', 'Live'),
(351, 'SS', 'Africa/Juba', 'Live'),
(352, 'ST', 'Africa/Sao_Tome', 'Live'),
(353, 'SV', 'America/El_Salvador', 'Live'),
(354, 'SX', 'America/Lower_Princes', 'Live'),
(355, 'SY', 'Asia/Damascus', 'Live'),
(356, 'SZ', 'Africa/Mbabane', 'Live'),
(357, 'TC', 'America/Grand_Turk', 'Live'),
(358, 'TD', 'Africa/Ndjamena', 'Live'),
(359, 'TF', 'Indian/Kerguelen', 'Live'),
(360, 'TG', 'Africa/Lome', 'Live'),
(361, 'TH', 'Asia/Bangkok', 'Live'),
(362, 'TJ', 'Asia/Dushanbe', 'Live'),
(363, 'TK', 'Pacific/Fakaofo', 'Live'),
(364, 'TL', 'Asia/Dili', 'Live'),
(365, 'TM', 'Asia/Ashgabat', 'Live'),
(366, 'TN', 'Africa/Tunis', 'Live'),
(367, 'TO', 'Pacific/Tongatapu', 'Live'),
(368, 'TR', 'Europe/Istanbul', 'Live'),
(369, 'TT', 'America/Port_of_Spain', 'Live'),
(370, 'TV', 'Pacific/Funafuti', 'Live'),
(371, 'TW', 'Asia/Taipei', 'Live'),
(372, 'TZ', 'Africa/Dar_es_Salaam', 'Live'),
(373, 'UA', 'Europe/Kiev', 'Live'),
(374, 'UA', 'Europe/Uzhgorod', 'Live'),
(375, 'UA', 'Europe/Zaporozhye', 'Live'),
(376, 'UG', 'Africa/Kampala', 'Live'),
(377, 'UM', 'Pacific/Midway', 'Live'),
(378, 'UM', 'Pacific/Wake', 'Live'),
(379, 'US', 'America/New_York', 'Live'),
(380, 'US', 'America/Detroit', 'Live'),
(381, 'US', 'America/Kentucky/Louisville', 'Live'),
(382, 'US', 'America/Kentucky/Monticello', 'Live'),
(383, 'US', 'America/Indiana/Indianapolis', 'Live'),
(384, 'US', 'America/Indiana/Vincennes', 'Live'),
(385, 'US', 'America/Indiana/Winamac', 'Live'),
(386, 'US', 'America/Indiana/Marengo', 'Live'),
(387, 'US', 'America/Indiana/Petersburg', 'Live'),
(388, 'US', 'America/Indiana/Vevay', 'Live'),
(389, 'US', 'America/Chicago', 'Live'),
(390, 'US', 'America/Indiana/Tell_City', 'Live'),
(391, 'US', 'America/Indiana/Knox', 'Live'),
(392, 'US', 'America/Menominee', 'Live'),
(393, 'US', 'America/North_Dakota/Center', 'Live'),
(394, 'US', 'America/North_Dakota/New_Salem', 'Live'),
(395, 'US', 'America/North_Dakota/Beulah', 'Live'),
(396, 'US', 'America/Denver', 'Live'),
(397, 'US', 'America/Boise', 'Live'),
(398, 'US', 'America/Phoenix', 'Live'),
(399, 'US', 'America/Los_Angeles', 'Live'),
(400, 'US', 'America/Anchorage', 'Live'),
(401, 'US', 'America/Juneau', 'Live'),
(402, 'US', 'America/Sitka', 'Live'),
(403, 'US', 'America/Metlakatla', 'Live'),
(404, 'US', 'America/Yakutat', 'Live'),
(405, 'US', 'America/Nome', 'Live'),
(406, 'US', 'America/Adak', 'Live'),
(407, 'US', 'Pacific/Honolulu', 'Live'),
(408, 'UY', 'America/Montevideo', 'Live'),
(409, 'UZ', 'Asia/Samarkand', 'Live'),
(410, 'UZ', 'Asia/Tashkent', 'Live'),
(411, 'VA', 'Europe/Vatican', 'Live'),
(412, 'VC', 'America/St_Vincent', 'Live'),
(413, 'VE', 'America/Caracas', 'Live'),
(414, 'VG', 'America/Tortola', 'Live'),
(415, 'VI', 'America/St_Thomas', 'Live'),
(416, 'VN', 'Asia/Ho_Chi_Minh', 'Live'),
(417, 'VU', 'Pacific/Efate', 'Live'),
(418, 'WF', 'Pacific/Wallis', 'Live'),
(419, 'WS', 'Pacific/Apia', 'Live'),
(420, 'YE', 'Asia/Aden', 'Live'),
(421, 'YT', 'Indian/Mayotte', 'Live'),
(422, 'ZA', 'Africa/Johannesburg', 'Live'),
(423, 'ZM', 'Africa/Lusaka', 'Live'),
(424, 'ZW', 'Africa/Harare', 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_transfer`
--

CREATE TABLE `tbl_transfer` (
  `id` int(10) NOT NULL,
  `transfer_type` int(11) NOT NULL DEFAULT '1',
  `reference_no` varchar(50) DEFAULT NULL,
  `date` varchar(15) NOT NULL,
  `received_date` date DEFAULT NULL,
  `note_for_sender` varchar(300) DEFAULT NULL,
  `note_for_receiver` varchar(300) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `from_outlet_id` int(11) DEFAULT NULL,
  `to_outlet_id` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_transfer_ingredients`
--

CREATE TABLE `tbl_transfer_ingredients` (
  `id` int(10) NOT NULL,
  `transfer_type` int(11) NOT NULL DEFAULT '1',
  `status` int(11) DEFAULT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `quantity_amount` float DEFAULT NULL,
  `unit_price` float DEFAULT NULL,
  `transfer_id` int(10) DEFAULT NULL,
  `from_outlet_id` int(10) DEFAULT NULL,
  `to_outlet_id` int(11) DEFAULT NULL,
  `total_cost` float DEFAULT NULL,
  `single_cost_total` float DEFAULT NULL,
  `single_total_tax` float DEFAULT NULL,
  `single_total_sale_amount` float DEFAULT NULL,
  `total_tax` float DEFAULT NULL,
  `total_sale_amount` float DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_transfer_received_ingredients`
--

CREATE TABLE `tbl_transfer_received_ingredients` (
  `id` int(10) NOT NULL,
  `transfer_type` int(11) NOT NULL DEFAULT '1',
  `status` int(11) DEFAULT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `quantity_amount` float DEFAULT NULL,
  `unit_price` float DEFAULT NULL,
  `transfer_id` int(10) DEFAULT NULL,
  `from_outlet_id` int(10) DEFAULT NULL,
  `to_outlet_id` int(11) DEFAULT NULL,
  `total_cost` float DEFAULT NULL,
  `single_cost_total` float DEFAULT NULL,
  `single_total_tax` float DEFAULT NULL,
  `single_total_sale_amount` float DEFAULT NULL,
  `total_tax` float DEFAULT NULL,
  `total_sale_amount` float DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_units`
--

CREATE TABLE `tbl_units` (
  `id` int(10) NOT NULL,
  `unit_name` varchar(10) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `company_id` int(1) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Dumping data for table `tbl_units`
--

INSERT INTO `tbl_units` (`id`, `unit_name`, `description`, `company_id`, `del_status`) VALUES
(1, 'Kg', 'Kilo Gram', 1, 'Live'),
(2, 'L', 'Liter', 1, 'Live'),
(3, 'Pcs', 'Piece', 1, 'Live'),
(4, 'g', 'gram', 1, 'Live'),
(5, 'ml', 'Mili Liter', 1, 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_users`
--

CREATE TABLE `tbl_users` (
  `id` int(10) NOT NULL,
  `full_name` varchar(25) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `email_address` varchar(50) NOT NULL,
  `password` varchar(50) NOT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `will_login` varchar(20) DEFAULT 'No',
  `role` varchar(25) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `outlets` varchar(100) DEFAULT NULL,
  `company_id` int(10) DEFAULT NULL,
  `account_creation_date` datetime DEFAULT NULL,
  `language` varchar(100) DEFAULT 'english',
  `last_login` datetime DEFAULT NULL,
  `active_status` varchar(25) DEFAULT 'Active',
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_users`
--

INSERT INTO `tbl_users` (`id`, `full_name`, `phone`, `email_address`, `password`, `designation`, `will_login`, `role`, `outlet_id`, `outlets`, `company_id`, `account_creation_date`, `language`, `last_login`, `active_status`, `del_status`) VALUES
(1, 'Admin User', '***********', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', NULL, 'Yes', 'Admin', 1, '1', 1, '2018-02-17 07:28:32', 'english', '2018-02-17 07:28:32', 'Active', 'Live'),
(21, 'Restaurant Admin', '1234567', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'Admin User', 'Yes', 'Admin', NULL, NULL, 13, NULL, 'english', NULL, 'Active', 'Live'),
(22, 'zak', '3333', '<EMAIL>', '', 'Waiter', 'No', NULL, 1, '1', 1, NULL, 'english', NULL, 'Active', 'Deleted'),
(23, 'Mr Joe', '123456', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'user', 'Yes', 'User', 1, '1', 1, NULL, 'english', NULL, 'Active', 'Deleted'),
(24, 'Vina ', '************', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'Waiter', 'Yes', NULL, 1, '1', 1, NULL, 'english', NULL, 'Active', 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `tbl_user_menu_access`
--

CREATE TABLE `tbl_user_menu_access` (
  `id` int(10) NOT NULL,
  `menu_id` int(10) DEFAULT '0',
  `user_id` int(10) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tbl_user_menu_access`
--

INSERT INTO `tbl_user_menu_access` (`id`, `menu_id`, `user_id`) VALUES
(1, 1, 1),
(2, 2, 1),
(3, 3, 1),
(4, 4, 1),
(6, 6, 1),
(7, 7, 1),
(8, 8, 1),
(9, 9, 1),
(10, 10, 1),
(11, 9, 1),
(12, 11, 1),
(46, 13, 1),
(103, 14, 1),
(104, 15, 1),
(118, 16, 1),
(133, 5, 1),
(134, 10, 1),
(135, 12, 1),
(140, 17, 1),
(141, 18, 1),
(142, 19, 1),
(470, 56, 39),
(471, 1, 39),
(472, 13, 6),
(473, 14, 6),
(474, 56, 6),
(475, 55, 6),
(476, 50, 6),
(477, 12, 6),
(478, 7, 6),
(479, 5, 6),
(480, 51, 6),
(481, 48, 6),
(482, 47, 6),
(483, 45, 6),
(484, 43, 6),
(485, 44, 6),
(486, 3, 6),
(487, 11, 6),
(488, 15, 6),
(489, 54, 6),
(490, 46, 6),
(491, 17, 6),
(492, 52, 6),
(493, 1, 6),
(494, 2, 6),
(495, 6, 6),
(496, 23, 6),
(497, 59, 6),
(498, 22, 6),
(499, 49, 6),
(500, 10, 6),
(501, 53, 6),
(502, 60, 6),
(503, 18, 6),
(504, 16, 6),
(505, 4, 6),
(509, 13, 5),
(510, 14, 5),
(511, 56, 5),
(512, 55, 5),
(513, 50, 5),
(514, 12, 5),
(515, 7, 5),
(516, 5, 5),
(517, 51, 5),
(518, 48, 5),
(519, 47, 5),
(520, 45, 5),
(521, 43, 5),
(522, 44, 5),
(523, 3, 5),
(524, 11, 5),
(525, 15, 5),
(526, 54, 5),
(527, 46, 5),
(528, 17, 5),
(529, 52, 5),
(530, 1, 5),
(531, 2, 5),
(532, 6, 5),
(533, 23, 5),
(534, 59, 5),
(535, 22, 5),
(536, 49, 5),
(537, 10, 5),
(538, 53, 5),
(539, 60, 5),
(540, 18, 5),
(541, 16, 5),
(542, 4, 5),
(577, 13, 6),
(578, 14, 6),
(579, 56, 6),
(580, 55, 6),
(581, 50, 6),
(582, 12, 6),
(583, 7, 6),
(584, 5, 6),
(585, 51, 6),
(586, 48, 6),
(587, 47, 6),
(588, 45, 6),
(589, 43, 6),
(590, 44, 6),
(591, 3, 6),
(592, 11, 6),
(593, 15, 6),
(594, 54, 6),
(595, 46, 6),
(596, 17, 6),
(597, 52, 6),
(598, 1, 6),
(599, 2, 6),
(600, 6, 6),
(601, 23, 6),
(602, 59, 6),
(603, 22, 6),
(604, 49, 6),
(605, 10, 6),
(606, 53, 6),
(607, 60, 6),
(608, 18, 6),
(609, 16, 6),
(610, 4, 6),
(680, 1, 2),
(681, 13, 3),
(682, 14, 3),
(683, 56, 3),
(684, 55, 3),
(685, 50, 3),
(686, 12, 3),
(687, 7, 3),
(688, 5, 3),
(689, 51, 3),
(690, 48, 3),
(691, 47, 3),
(692, 45, 3),
(693, 43, 3),
(694, 44, 3),
(695, 3, 3),
(696, 11, 3),
(697, 15, 3),
(698, 54, 3),
(699, 46, 3),
(700, 17, 3),
(701, 52, 3),
(702, 62, 3),
(703, 1, 3),
(704, 2, 3),
(705, 6, 3),
(706, 23, 3),
(707, 59, 3),
(708, 22, 3),
(709, 49, 3),
(710, 10, 3),
(711, 53, 3),
(712, 60, 3),
(713, 18, 3),
(714, 16, 3),
(715, 4, 3),
(716, 77, 3),
(717, 13, 23),
(754, 13, 24),
(755, 14, 24),
(756, 56, 24),
(757, 55, 24),
(758, 50, 24),
(759, 12, 24),
(760, 7, 24),
(761, 5, 24),
(762, 51, 24),
(763, 48, 24),
(764, 47, 24),
(765, 45, 24),
(766, 43, 24),
(767, 44, 24),
(768, 3, 24),
(769, 11, 24),
(770, 79, 24),
(771, 15, 24),
(772, 54, 24),
(773, 46, 24),
(774, 17, 24),
(775, 52, 24),
(776, 1, 24),
(777, 2, 24),
(778, 6, 24),
(779, 23, 24),
(780, 59, 24),
(781, 22, 24),
(782, 49, 24),
(783, 10, 24),
(784, 53, 24),
(785, 60, 24),
(786, 18, 24),
(787, 16, 24),
(788, 4, 24),
(789, 77, 24);

-- --------------------------------------------------------

--
-- Table structure for table `tbl_wastes`
--

CREATE TABLE `tbl_wastes` (
  `id` int(11) NOT NULL,
  `reference_no` varchar(50) DEFAULT NULL,
  `date` date NOT NULL,
  `total_loss` float(10,2) DEFAULT NULL,
  `note` varchar(200) DEFAULT NULL,
  `employee_id` int(10) DEFAULT NULL,
  `user_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(50) DEFAULT 'Live',
  `food_menu_id` int(11) DEFAULT NULL,
  `food_menu_waste_qty` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `tbl_waste_ingredients`
--

CREATE TABLE `tbl_waste_ingredients` (
  `id` int(10) NOT NULL,
  `ingredient_id` int(10) DEFAULT NULL,
  `waste_amount` float(10,2) DEFAULT NULL,
  `last_purchase_price` float(10,2) DEFAULT NULL,
  `loss_amount` float(10,2) DEFAULT NULL,
  `waste_id` int(10) DEFAULT NULL,
  `outlet_id` int(10) DEFAULT NULL,
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `tbl_admin_user_menus`
--
ALTER TABLE `tbl_admin_user_menus`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_attendance`
--
ALTER TABLE `tbl_attendance`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_companies`
--
ALTER TABLE `tbl_companies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_customers`
--
ALTER TABLE `tbl_customers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_customer_due_receives`
--
ALTER TABLE `tbl_customer_due_receives`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_expenses`
--
ALTER TABLE `tbl_expenses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_expense_items`
--
ALTER TABLE `tbl_expense_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_food_menus`
--
ALTER TABLE `tbl_food_menus`
  ADD PRIMARY KEY (`id`),
  ADD KEY `company_del` (`company_id`,`del_status`),
  ADD KEY `id_del` (`id`,`del_status`);

--
-- Indexes for table `tbl_food_menus_ingredients`
--
ALTER TABLE `tbl_food_menus_ingredients`
  ADD PRIMARY KEY (`id`),
  ADD KEY `food_menu_del` (`food_menu_id`,`del_status`);

--
-- Indexes for table `tbl_food_menus_modifiers`
--
ALTER TABLE `tbl_food_menus_modifiers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_food_menu_categories`
--
ALTER TABLE `tbl_food_menu_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_holds`
--
ALTER TABLE `tbl_holds`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_holds_details`
--
ALTER TABLE `tbl_holds_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_holds_details_modifiers`
--
ALTER TABLE `tbl_holds_details_modifiers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_holds_table`
--
ALTER TABLE `tbl_holds_table`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_ingredients`
--
ALTER TABLE `tbl_ingredients`
  ADD PRIMARY KEY (`id`),
  ADD KEY `del` (`del_status`);

--
-- Indexes for table `tbl_ingredient_categories`
--
ALTER TABLE `tbl_ingredient_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_inventory_adjustment`
--
ALTER TABLE `tbl_inventory_adjustment`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_inventory_adjustment_ingredients`
--
ALTER TABLE `tbl_inventory_adjustment_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_menu_list`
--
ALTER TABLE `tbl_menu_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_modifiers`
--
ALTER TABLE `tbl_modifiers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_modifier_ingredients`
--
ALTER TABLE `tbl_modifier_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_notifications`
--
ALTER TABLE `tbl_notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_notification_bar_kitchen_panel`
--
ALTER TABLE `tbl_notification_bar_kitchen_panel`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_orders_table`
--
ALTER TABLE `tbl_orders_table`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_outlets`
--
ALTER TABLE `tbl_outlets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_payment_histories`
--
ALTER TABLE `tbl_payment_histories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_payment_methods`
--
ALTER TABLE `tbl_payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_plugins`
--
ALTER TABLE `tbl_plugins`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_pricing_plans`
--
ALTER TABLE `tbl_pricing_plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_printers`
--
ALTER TABLE `tbl_printers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_purchase`
--
ALTER TABLE `tbl_purchase`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_purchase_ingredients`
--
ALTER TABLE `tbl_purchase_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_register`
--
ALTER TABLE `tbl_register`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_sales`
--
ALTER TABLE `tbl_sales`
  ADD PRIMARY KEY (`id`),
  ADD KEY `os_del_out` (`order_status`,`del_status`,`outlet_id`),
  ADD KEY `id_del_outlet` (`id`,`del_status`,`outlet_id`),
  ADD KEY `outlet_order_status` (`outlet_id`,`order_status`),
  ADD KEY `id_del` (`id`,`del_status`),
  ADD KEY `user_date_time_del_order` (`user_id`,`date_time`,`del_status`,`order_status`),
  ADD KEY `table_id_status` (`table_id`,`order_status`),
  ADD KEY `outlet_id_waiter_id_order_status` (`outlet_id`,`waiter_id`,`order_status`);

--
-- Indexes for table `tbl_sales_details`
--
ALTER TABLE `tbl_sales_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_sales_details_modifiers`
--
ALTER TABLE `tbl_sales_details_modifiers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sales_id_details_id` (`sales_id`,`sales_details_id`);

--
-- Indexes for table `tbl_sale_consumptions`
--
ALTER TABLE `tbl_sale_consumptions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_sale_consumptions_of_menus`
--
ALTER TABLE `tbl_sale_consumptions_of_menus`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_sale_consumptions_of_modifiers_of_menus`
--
ALTER TABLE `tbl_sale_consumptions_of_modifiers_of_menus`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_sessions`
--
ALTER TABLE `tbl_sessions`
  ADD KEY `ci_sessions_timestamp` (`timestamp`);

--
-- Indexes for table `tbl_settings`
--
ALTER TABLE `tbl_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_suppliers`
--
ALTER TABLE `tbl_suppliers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_supplier_payments`
--
ALTER TABLE `tbl_supplier_payments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_tables`
--
ALTER TABLE `tbl_tables`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_temp_kot`
--
ALTER TABLE `tbl_temp_kot`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_time_zone`
--
ALTER TABLE `tbl_time_zone`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_transfer`
--
ALTER TABLE `tbl_transfer`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- Indexes for table `tbl_transfer_ingredients`
--
ALTER TABLE `tbl_transfer_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_transfer_received_ingredients`
--
ALTER TABLE `tbl_transfer_received_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_units`
--
ALTER TABLE `tbl_units`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_users`
--
ALTER TABLE `tbl_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_user_menu_access`
--
ALTER TABLE `tbl_user_menu_access`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_wastes`
--
ALTER TABLE `tbl_wastes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tbl_waste_ingredients`
--
ALTER TABLE `tbl_waste_ingredients`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `tbl_admin_user_menus`
--
ALTER TABLE `tbl_admin_user_menus`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT for table `tbl_attendance`
--
ALTER TABLE `tbl_attendance`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `tbl_companies`
--
ALTER TABLE `tbl_companies`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tbl_customers`
--
ALTER TABLE `tbl_customers`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `tbl_customer_due_receives`
--
ALTER TABLE `tbl_customer_due_receives`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_expenses`
--
ALTER TABLE `tbl_expenses`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_expense_items`
--
ALTER TABLE `tbl_expense_items`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_food_menus`
--
ALTER TABLE `tbl_food_menus`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=61;

--
-- AUTO_INCREMENT for table `tbl_food_menus_ingredients`
--
ALTER TABLE `tbl_food_menus_ingredients`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_food_menus_modifiers`
--
ALTER TABLE `tbl_food_menus_modifiers`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_food_menu_categories`
--
ALTER TABLE `tbl_food_menu_categories`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `tbl_holds`
--
ALTER TABLE `tbl_holds`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_holds_details`
--
ALTER TABLE `tbl_holds_details`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_holds_details_modifiers`
--
ALTER TABLE `tbl_holds_details_modifiers`
  MODIFY `id` int(15) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_holds_table`
--
ALTER TABLE `tbl_holds_table`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_ingredients`
--
ALTER TABLE `tbl_ingredients`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_ingredient_categories`
--
ALTER TABLE `tbl_ingredient_categories`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_inventory_adjustment`
--
ALTER TABLE `tbl_inventory_adjustment`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_inventory_adjustment_ingredients`
--
ALTER TABLE `tbl_inventory_adjustment_ingredients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_menu_list`
--
ALTER TABLE `tbl_menu_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_modifiers`
--
ALTER TABLE `tbl_modifiers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_modifier_ingredients`
--
ALTER TABLE `tbl_modifier_ingredients`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_notifications`
--
ALTER TABLE `tbl_notifications`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tbl_notification_bar_kitchen_panel`
--
ALTER TABLE `tbl_notification_bar_kitchen_panel`
  MODIFY `id` int(15) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tbl_orders_table`
--
ALTER TABLE `tbl_orders_table`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `tbl_outlets`
--
ALTER TABLE `tbl_outlets`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tbl_payment_histories`
--
ALTER TABLE `tbl_payment_histories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_payment_methods`
--
ALTER TABLE `tbl_payment_methods`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `tbl_plugins`
--
ALTER TABLE `tbl_plugins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tbl_pricing_plans`
--
ALTER TABLE `tbl_pricing_plans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `tbl_printers`
--
ALTER TABLE `tbl_printers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_purchase`
--
ALTER TABLE `tbl_purchase`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_purchase_ingredients`
--
ALTER TABLE `tbl_purchase_ingredients`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_register`
--
ALTER TABLE `tbl_register`
  MODIFY `id` int(15) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `tbl_sales`
--
ALTER TABLE `tbl_sales`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `tbl_sales_details`
--
ALTER TABLE `tbl_sales_details`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=104;

--
-- AUTO_INCREMENT for table `tbl_sales_details_modifiers`
--
ALTER TABLE `tbl_sales_details_modifiers`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_sale_consumptions`
--
ALTER TABLE `tbl_sale_consumptions`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `tbl_sale_consumptions_of_menus`
--
ALTER TABLE `tbl_sale_consumptions_of_menus`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_sale_consumptions_of_modifiers_of_menus`
--
ALTER TABLE `tbl_sale_consumptions_of_modifiers_of_menus`
  MODIFY `id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_settings`
--
ALTER TABLE `tbl_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `tbl_suppliers`
--
ALTER TABLE `tbl_suppliers`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_supplier_payments`
--
ALTER TABLE `tbl_supplier_payments`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_tables`
--
ALTER TABLE `tbl_tables`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `tbl_temp_kot`
--
ALTER TABLE `tbl_temp_kot`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_time_zone`
--
ALTER TABLE `tbl_time_zone`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=425;

--
-- AUTO_INCREMENT for table `tbl_transfer`
--
ALTER TABLE `tbl_transfer`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_transfer_ingredients`
--
ALTER TABLE `tbl_transfer_ingredients`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_transfer_received_ingredients`
--
ALTER TABLE `tbl_transfer_received_ingredients`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_units`
--
ALTER TABLE `tbl_units`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `tbl_users`
--
ALTER TABLE `tbl_users`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `tbl_user_menu_access`
--
ALTER TABLE `tbl_user_menu_access`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=790;

--
-- AUTO_INCREMENT for table `tbl_wastes`
--
ALTER TABLE `tbl_wastes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tbl_waste_ingredients`
--
ALTER TABLE `tbl_waste_ingredients`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
