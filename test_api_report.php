<?php
/**
 * Test file untuk API Report iRestora PLUS
 * Jalankan file ini untuk test API yang baru dibuat
 */

// Konfigurasi
$base_url = 'http://localhost/irestora_api'; // Sesuaikan dengan URL Anda
$outlet_id = 1; // Sesuaikan dengan outlet_id yang valid
$company_id = 1; // Sesuaikan dengan company_id yang valid

echo "=== TEST API REPORT IRESTORA PLUS ===\n\n";

// Test 1: Connection Test
echo "1. Testing API Connection...\n";
$test_url = $base_url . '/index.php/Api_Report/test_connection';
$response = file_get_contents($test_url);
$data = json_decode($response, true);

if ($data && $data['status']) {
    echo "✓ Connection successful\n";
    echo "  Version: " . $data['version'] . "\n";
    echo "  Timestamp: " . $data['timestamp'] . "\n\n";
} else {
    echo "✗ Connection failed\n";
    echo "  Response: " . $response . "\n\n";
    exit;
}

// Test 1.5: Debug Data
echo "1.5. Testing Debug Data...\n";
$debug_url = $base_url . '/index.php/Api_Report/debug_data';
$debug_data = array(
    'date' => '2025-07-22',
    'outlet_id' => $outlet_id,
    'company_id' => $company_id
);

$debug_response = callAPI('POST', $debug_url, $debug_data);
$debug_result = json_decode($debug_response, true);

if ($debug_result && $debug_result['status']) {
    echo "✓ Debug data successful\n";
    echo "  Outlet Info: " . ($debug_result['debug_info']['outlet_info'] ? 'Found' : 'Not Found') . "\n";
    echo "  Sales Summary Count: " . count($debug_result['debug_info']['sales_summary']) . "\n";
    echo "  Sales for Date Count: " . count($debug_result['debug_info']['sales_for_date']) . "\n\n";
} else {
    echo "✗ Debug data failed\n";
    if ($debug_result) {
        echo "  Error: " . $debug_result['message'] . "\n\n";
    } else {
        echo "  Response: " . $debug_response . "\n\n";
    }
}

// Test 2: Daily Report
echo "2. Testing Daily Report...\n";
$daily_url = $base_url . '/index.php/Api_Report/daily_summary';
$daily_data = array(
    'date' => '2025-07-21', // Tanggal yang ada data
    'outlet_id' => $outlet_id,
    'company_id' => $company_id
);

$daily_response = callAPI('POST', $daily_url, $daily_data);
$daily_result = json_decode($daily_response, true);

if ($daily_result && $daily_result['status']) {
    echo "✓ Daily report successful\n";
    echo "  Date: " . $daily_result['data']['report_period'] . "\n";
    echo "  Outlet: " . $daily_result['data']['outlet_name'] . "\n";
    echo "  Sales Count: " . $daily_result['data']['summary']['sales']['count'] . "\n";
    echo "  Sales Amount: " . number_format($daily_result['data']['summary']['sales']['total_amount']) . "\n";
    echo "  Net Cash Flow: " . number_format($daily_result['data']['summary']['net_cash_flow']) . "\n\n";
} else {
    echo "✗ Daily report failed\n";
    if ($daily_result) {
        echo "  Error: " . $daily_result['message'] . "\n";
        if (isset($daily_result['data']['error_details'])) {
            echo "  Debug Info: " . substr($daily_result['data']['error_details'], 0, 200) . "...\n";
        }
        echo "\n";
    } else {
        echo "  Response: " . $daily_response . "\n\n";
    }
}

// Test 2.5: Daily Range Report
echo "2.5. Testing Daily Range Report...\n";
$range_data = array(
    'start_date' => '2025-07-20',
    'end_date' => '2025-07-21',
    'outlet_id' => $outlet_id,
    'company_id' => $company_id
);

$range_response = callAPI('POST', $daily_url, $range_data);
$range_result = json_decode($range_response, true);

if ($range_result && $range_result['status']) {
    echo "✓ Range report successful\n";
    echo "  Period: " . $range_result['data']['report_period'] . "\n";
    echo "  Type: " . $range_result['data']['report_type'] . "\n";
    echo "  Sales Count: " . $range_result['data']['summary']['sales']['count'] . "\n";
    echo "  Sales Amount: " . number_format($range_result['data']['summary']['sales']['total_amount']) . "\n";
    $daily_breakdown_count = isset($range_result['data']['daily_breakdown']) ? count($range_result['data']['daily_breakdown']) : 0;
    echo "  Daily Breakdown: " . $daily_breakdown_count . " days\n\n";
} else {
    echo "✗ Range report failed\n";
    if ($range_result) {
        echo "  Error: " . $range_result['message'] . "\n\n";
    } else {
        echo "  Response: " . $range_response . "\n\n";
    }
}

// Test 3: Monthly Report
echo "3. Testing Monthly Report...\n";
$monthly_url = $base_url . '/index.php/Api_Report/monthly_summary';
$monthly_data = array(
    'month' => date('n'), // Bulan ini
    'year' => date('Y'), // Tahun ini
    'outlet_id' => $outlet_id,
    'company_id' => $company_id
);

$monthly_response = callAPI('POST', $monthly_url, $monthly_data);
$monthly_result = json_decode($monthly_response, true);

if ($monthly_result && $monthly_result['status']) {
    echo "✓ Monthly report successful\n";
    echo "  Month: " . $monthly_result['data']['month_name'] . " " . $monthly_result['data']['report_year'] . "\n";
    echo "  Outlet: " . $monthly_result['data']['outlet_name'] . "\n";
    echo "  Sales Count: " . $monthly_result['data']['summary']['sales']['count'] . "\n";
    echo "  Sales Amount: " . number_format($monthly_result['data']['summary']['sales']['total_amount']) . "\n";
    echo "  Net Cash Flow: " . number_format($monthly_result['data']['summary']['net_cash_flow']) . "\n";
    echo "  Daily Breakdown Count: " . count($monthly_result['data']['daily_breakdown']) . " days\n\n";
} else {
    echo "✗ Monthly report failed\n";
    if ($monthly_result) {
        echo "  Error: " . $monthly_result['message'] . "\n\n";
    } else {
        echo "  Response: " . $monthly_response . "\n\n";
    }
}

// Test 4: Error Handling
echo "4. Testing Error Handling...\n";
$error_data = array(
    'date' => 'invalid-date',
    'outlet_id' => 999999, // Invalid outlet
    'company_id' => 999999  // Invalid company
);

$error_response = callAPI('POST', $daily_url, $error_data);
$error_result = json_decode($error_response, true);

if ($error_result && !$error_result['status']) {
    echo "✓ Error handling working correctly\n";
    echo "  Error message: " . $error_result['message'] . "\n\n";
} else {
    echo "✗ Error handling not working as expected\n\n";
}

echo "=== TEST COMPLETED ===\n";

/**
 * Helper function untuk call API
 */
function callAPI($method, $url, $data = false) {
    $curl = curl_init();

    switch ($method) {
        case "POST":
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data)
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            break;
        case "PUT":
            curl_setopt($curl, CURLOPT_PUT, 1);
            break;
        default:
            if ($data)
                $url = sprintf("%s?%s", $url, http_build_query($data));
    }

    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));

    $result = curl_exec($curl);
    curl_close($curl);

    return $result;
}

/**
 * Contoh penggunaan untuk sistem eksternal
 */
function exampleUsage() {
    global $base_url, $outlet_id, $company_id;
    
    echo "\n=== EXAMPLE USAGE FOR EXTERNAL SYSTEM ===\n\n";
    
    // Contoh 1: Ambil laporan harian untuk dashboard
    echo "// Contoh 1: Ambil laporan harian\n";
    echo '$daily_report = getDailyReport("' . date('Y-m-d') . '", ' . $outlet_id . ', ' . $company_id . ');' . "\n";
    echo 'if ($daily_report) {' . "\n";
    echo '    $total_sales = $daily_report["summary"]["sales"]["total_amount"];' . "\n";
    echo '    $net_cash_flow = $daily_report["summary"]["net_cash_flow"];' . "\n";
    echo '    echo "Today Sales: " . number_format($total_sales);' . "\n";
    echo '}' . "\n\n";
    
    // Contoh 2: Ambil laporan bulanan untuk analisis
    echo "// Contoh 2: Ambil laporan bulanan\n";
    echo '$monthly_report = getMonthlyReport(' . date('n') . ', ' . date('Y') . ', ' . $outlet_id . ', ' . $company_id . ');' . "\n";
    echo 'if ($monthly_report) {' . "\n";
    echo '    foreach ($monthly_report["daily_breakdown"] as $day) {' . "\n";
    echo '        echo $day["date"] . ": " . number_format($day["sales"]["total_amount"]) . "\n";' . "\n";
    echo '    }' . "\n";
    echo '}' . "\n\n";
    
    // Contoh 3: Integrasi dengan sistem lain
    echo "// Contoh 3: Kirim data ke sistem eksternal\n";
    echo '$report = getDailyReport(date("Y-m-d"), ' . $outlet_id . ', ' . $company_id . ');' . "\n";
    echo 'if ($report) {' . "\n";
    echo '    // Kirim ke sistem accounting' . "\n";
    echo '    $accounting_data = [' . "\n";
    echo '        "date" => $report["report_date"],' . "\n";
    echo '        "sales" => $report["summary"]["sales"]["total_amount"],' . "\n";
    echo '        "expenses" => $report["summary"]["expenses"]["total_amount"],' . "\n";
    echo '        "net_income" => $report["summary"]["net_cash_flow"]' . "\n";
    echo '    ];' . "\n";
    echo '    sendToAccountingSystem($accounting_data);' . "\n";
    echo '}' . "\n\n";
}

// Uncomment untuk melihat contoh penggunaan
// exampleUsage();
?>
